# EdenRP - Arma Reforger Roleplay Framework

EdenRP is a complete conversion of the Olympus Altis Life framework from Arma 3 to Arma Reforger. This project provides a comprehensive roleplay experience with economy systems, law enforcement, gangs, civilian activities, and much more.

## Project Status

**Current Phase**: Core Framework Setup ✅
- Basic mod structure established
- Core manager components created
- Player and economy systems foundation implemented
- Configuration system set up

**Next Phase**: Player System Implementation

## Features

### Core Systems
- **Player Management**: Character data, factions, licenses, statistics
- **Dynamic Economy**: Market-based pricing, banking, ATM systems
- **Law Enforcement**: APD/AHP systems, arrests, jail, wanted system
- **Gang Systems**: Territory control, gang wars, shared resources
- **Civilian Activities**: Legal/illegal jobs, licensing, property ownership
- **Medical System**: EMS faction, revive mechanics, hospital interactions
- **Vehicle System**: Ownership, garages, impound system
- **Housing System**: Property ownership, storage, real estate market

### Converted from Olympus Altis Life
- 78+ player statistics tracking
- Dynamic market with 30+ items (legal and illegal)
- Complete faction system (Civilian, Police, Medical, Gang)
- Comprehensive database schema
- All original Olympus features and mechanics

## Installation & Packaging

### For Developers
1. Clone/download the EdenRP project
2. Open Arma Reforger Tools (Workbench)
3. Load the EdenRP project (addon.gproj)
4. Configure database settings in `Configs/EdenRPConfig.conf`
5. Follow the **PACKAGING_GUIDE.md** for building and distribution

### Quick Build
Run `build_mod.bat` to automatically build the mod using Workbench command line tools.

### For Server Operators
1. Download EdenRP from Steam Workshop or direct distribution
2. Configure server with EdenRP mod enabled
3. Set up MySQL database using provided schema
4. Follow **DEPLOYMENT_GUIDE.md** for complete setup

## Development Structure

```
EdenRP/
├── Scripts/Game/EdenRP/
│   ├── Core/              # Core framework components
│   ├── Player/            # Player management system
│   ├── Economy/           # Economic systems
│   ├── Vehicles/          # Vehicle management
│   ├── Housing/           # Property systems
│   ├── Gangs/             # Gang and territory systems
│   ├── Law/               # Law enforcement systems
│   ├── Medical/           # Medical and EMS systems
│   ├── UI/                # User interface components
│   └── Admin/             # Administration tools
├── Configs/               # Configuration files
├── Prefabs/               # Game object prefabs
├── UI/                    # User interface layouts
└── Worlds/                # World modifications
```

## Configuration

Edit `Configs/EdenRPConfig.conf` to customize:
- Server settings and paycheck amounts
- Market item prices and legality
- Faction configurations
- Database connection settings
- Title colors and permissions

## Database Setup

EdenRP requires a MySQL database with the following tables:
- `players` - Player data and statistics
- `vehicles` - Vehicle ownership
- `houses` - Property ownership
- `gangs` - Gang management
- `market` - Dynamic market data
- And many more (see CONVERSION_ANALYSIS.md)

## Contributing

This is a comprehensive conversion project. Key areas for development:
1. Complete player system implementation
2. Database integration layer
3. UI system development
4. Vehicle and housing systems
5. Gang and territory mechanics
6. Law enforcement tools
7. Medical system implementation

## License

This project is a conversion of the Olympus Altis Life framework for educational and community use.

## Credits

- Original Olympus Altis Life framework developers
- EdenRP Development Team
- Arma Reforger modding community