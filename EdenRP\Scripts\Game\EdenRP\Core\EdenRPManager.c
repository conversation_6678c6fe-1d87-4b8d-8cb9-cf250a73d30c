//! EdenRP Core Manager Component
//! Manages the core EdenRP roleplay framework systems
//! Converted from Olympus Altis Life for Arma Reforger

[ComponentEditorProps(category: "EdenRP", description: "Core EdenRP Manager")]
class EdenRPManagerComponentClass : ScriptComponentClass
{
}

//! Core manager for EdenRP roleplay framework
class EdenRPManagerComponent : ScriptComponent
{
	[Attribute("true", UIWidgets.CheckBox, "Enable EdenRP System")]
	protected bool m_bEnabled;

	[Attribute("1", UIWidgets.SpinBox, "Server ID", "1 10 1")]
	protected int m_iServerId;

	[Attribute("", UIWidgets.EditBox, "Database Connection String")]
	protected string m_sDatabaseConnection;

	// Core system references
	protected ref EdenRPPlayerManager m_PlayerManager;
	protected ref EdenRPEconomyManager m_EconomyManager;
	protected ref EdenRPVehicleManager m_VehicleManager;
	protected ref EdenRPHousingManager m_HousingManager;
	protected ref EdenRPGangManager m_GangManager;
	protected ref EdenRPLawManager m_LawManager;
	protected ref EdenRPMedicalManager m_MedicalManager;

	// Static instance for global access
	protected static EdenRPManagerComponent s_Instance;

	//! Get the singleton instance
	static EdenRPManagerComponent GetInstance()
	{
		return s_Instance;
	}

	//! Initialize the EdenRP system
	override void OnPostInit(IEntity owner)
	{
		super.OnPostInit(owner);

		if (!m_bEnabled)
			return;

		s_Instance = this;

		// Initialize core systems
		InitializeSystems();

		Print("[EdenRP] Core Manager initialized successfully", LogLevel.NORMAL);
	}

	//! Initialize all core EdenRP systems
	protected void InitializeSystems()
	{
		// Initialize in dependency order
		m_PlayerManager = new EdenRPPlayerManager();
		m_EconomyManager = new EdenRPEconomyManager();
		m_VehicleManager = new EdenRPVehicleManager();
		m_HousingManager = new EdenRPHousingManager();
		m_GangManager = new EdenRPGangManager();
		m_LawManager = new EdenRPLawManager();
		m_MedicalManager = new EdenRPMedicalManager();

		// Initialize each system
		m_PlayerManager.Initialize();
		m_EconomyManager.Initialize();
		m_VehicleManager.Initialize();
		m_HousingManager.Initialize();
		m_GangManager.Initialize();
		m_LawManager.Initialize();
		m_MedicalManager.Initialize();
	}

	//! Get player manager
	EdenRPPlayerManager GetPlayerManager()
	{
		return m_PlayerManager;
	}

	//! Get economy manager
	EdenRPEconomyManager GetEconomyManager()
	{
		return m_EconomyManager;
	}

	//! Get vehicle manager
	EdenRPVehicleManager GetVehicleManager()
	{
		return m_VehicleManager;
	}

	//! Get housing manager
	EdenRPHousingManager GetHousingManager()
	{
		return m_HousingManager;
	}

	//! Get gang manager
	EdenRPGangManager GetGangManager()
	{
		return m_GangManager;
	}

	//! Get law enforcement manager
	EdenRPLawManager GetLawManager()
	{
		return m_LawManager;
	}

	//! Get medical manager
	EdenRPMedicalManager GetMedicalManager()
	{
		return m_MedicalManager;
	}

	//! Get server ID
	int GetServerId()
	{
		return m_iServerId;
	}

	//! Get database connection string
	string GetDatabaseConnection()
	{
		return m_sDatabaseConnection;
	}

	//! Cleanup on destruction
	override void OnDelete(IEntity owner)
	{
		if (s_Instance == this)
			s_Instance = null;

		super.OnDelete(owner);
	}
}