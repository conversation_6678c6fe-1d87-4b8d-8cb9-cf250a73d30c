//! EdenRP Player Manager
//! Manages player data, factions, licenses, and statistics
//! Converted from Olympus Altis Life player system

//! Player faction enumeration
enum EdenRPFaction
{
	CIVILIAN = 0,
	POLICE = 1,
	MEDICAL = 2,
	GANG = 3
}

//! Player statistics enumeration (from Olympus system)
enum EdenRPPlayerStats
{
	MARIJUANA = 0, HEROIN = 1, COCAINE = 2, METH = 3, MUSHROOM = 4, FROG = 5,
	OIL = 6, IRON = 7, DIAMOND = 8, GLASS = 9, CEMENT = 10, PLATINUM = 11,
	MOONSHINE = 12, FISH_NUM = 13, SALT = 14, SILVER = 15, COPPER = 16,
	GOLDBAR = 17, TURTLE = 18, REDGULL = 19, COFFEE = 20, LOCKPICK_FAIL = 21,
	LOCKPICK_SUCCESS = 22, BLAST_CHARGE = 23, EPIPEN = 24, SPEED_BOMB = 25,
	SALVAGE_NUM = 26, SALVAGE_MONEY = 27, REVIVES_MED = 28, CONTRABAND = 29,
	COP_MONEY = 30, BLOOD_BAG = 31, TICKETS_PAID = 32, TICKETS_VAL = 33,
	DEFUSES = 34, KIDNEYS = 35, FISH_MONEY = 36, TITAN_HITS = 37,
	BETS_WON = 38, BETS_LOST = 39, BETS_WON_VALUE = 40, BETS_LOST_VALUE = 41,
	KILLS = 42, DEATHS = 43, REVIVES = 44, DISTANCE_FOOT = 45, DISTANCE_VEHICLE = 46,
	BOUNTIES_RECEIVED = 47, ARRESTS_MADE = 48, PLAYTIME_CIV = 49, PLAYTIME_COP = 50,
	PLAYTIME_MED = 51, TELEPORTS = 52, VEHICLES_CHOPPED = 53, COPS_ROBBED = 54,
	JAIL_ESCAPES = 55, MONEY_SPENT = 56, EVENTS_WON = 57, KILLS_1KM = 58,
	CONQ_KILLS = 59, CONQ_DEATHS = 60, CONQ_CAPTURES = 61, CASINO_WINNINGS = 62,
	CASINO_LOSSES = 63, CASINO_USES = 64, LETHAL_INJECTIONS = 65, DONUTS = 66,
	DRUGS_SEIZED_CURRENCY = 67, VIGIARRESTS = 68, GOKART_TIME = 69, MED_TOOLKITS = 70,
	AA_REPAIRED = 71, MED_IMPOUNDS = 72, HITS_CLAIMED = 73, HITS_PLACED = 74,
	AA_HACKED = 75, COP_LETHALS = 76, PARDONS = 77
}

//! Player data structure
class EdenRPPlayerData
{
	string m_sPlayerID;
	string m_sPlayerName;
	int m_iCash;
	int m_iBankAccount;
	EdenRPFaction m_eFaction;
	int m_iFactionRank;
	ref array<string> m_aLicenses;
	ref array<int> m_aStatistics;
	ref array<string> m_aTitles;
	string m_sCurrentTitle;
	vector m_vTitleColor;

	// Additional Olympus data
	int m_iAdminLevel;
	int m_iDesignerLevel;
	int m_iDeveloperLevel;
	int m_iCivCouncilLevel;
	bool m_bRestricted;
	int m_iDonorLevel;
	ref array<string> m_aAliases;
	ref array<int> m_aWantedCrimes;
	bool m_bIsArrested;
	int m_iDepositBox;
	float m_fLastActive;
	float m_fJoinedTimestamp;

	void EdenRPPlayerData()
	{
		m_aLicenses = new array<string>();
		m_aStatistics = new array<int>();
		m_aTitles = new array<string>();
		m_aAliases = new array<string>();
		m_aWantedCrimes = new array<int>();

		// Initialize statistics array (78 stats from Olympus)
		m_aStatistics.Resize(78);
		for (int i = 0; i < 78; i++)
		{
			m_aStatistics[i] = 0;
		}

		// Initialize wanted crimes array (75 crimes from Olympus)
		m_aWantedCrimes.Resize(75);
		for (int i = 0; i < 75; i++)
		{
			m_aWantedCrimes[i] = 0;
		}

		// Set defaults
		m_iAdminLevel = 0;
		m_iDesignerLevel = 0;
		m_iDeveloperLevel = 0;
		m_iCivCouncilLevel = 0;
		m_bRestricted = false;
		m_iDonorLevel = 0;
		m_bIsArrested = false;
		m_iDepositBox = 0;
		m_fLastActive = 0;
		m_fJoinedTimestamp = 0;
	}

	//! Update player statistic
	void UpdateStatistic(EdenRPPlayerStats statType, int value)
	{
		if (statType >= 0 && statType < m_aStatistics.Count())
		{
			m_aStatistics[statType] += value;
		}
	}

	//! Get player statistic
	int GetStatistic(EdenRPPlayerStats statType)
	{
		if (statType >= 0 && statType < m_aStatistics.Count())
		{
			return m_aStatistics[statType];
		}
		return 0;
	}

	//! Check if player has license
	bool HasLicense(string licenseType)
	{
		return m_aLicenses.Find(licenseType) != -1;
	}

	//! Add license to player
	void AddLicense(string licenseType)
	{
		if (!HasLicense(licenseType))
		{
			m_aLicenses.Insert(licenseType);
		}
	}

	//! Remove license from player
	void RemoveLicense(string licenseType)
	{
		int index = m_aLicenses.Find(licenseType);
		if (index != -1)
		{
			m_aLicenses.Remove(index);
		}
	}
}

//! Player Manager for EdenRP
class EdenRPPlayerManager
{
	// Player data storage
	protected ref map<string, ref EdenRPPlayerData> m_mPlayerData;
	protected ref array<string> m_aConnectedPlayers;

	// License system
	protected ref map<string, int> m_mLicensePrices;
	protected ref array<string> m_aAvailableLicenses;

	// Configuration
	protected int m_iPaycheckAmount = 450; // From Olympus configuration
	protected int m_iPaycheckInterval = 300000; // 5 minutes in milliseconds
	protected int m_iStartingCash = 0;
	protected int m_iStartingBank = ********;

	// Session management
	protected ref map<string, bool> m_mSessionCompleted;
	protected ref map<string, int> m_mSessionTries;

	void EdenRPPlayerManager()
	{
		m_mPlayerData = new map<string, ref EdenRPPlayerData>();
		m_aConnectedPlayers = new array<string>();
		m_mLicensePrices = new map<string, int>();
		m_aAvailableLicenses = new array<string>();
		m_mSessionCompleted = new map<string, bool>();
		m_mSessionTries = new map<string, int>();
	}

	//! Initialize the player manager
	void Initialize()
	{
		Print("[EdenRP] Player Manager initialized", LogLevel.NORMAL);

		// Initialize license system
		InitializeLicenseSystem();

		// Start paycheck system
		GetGame().GetCallqueue().CallLater(ProcessPaychecks, m_iPaycheckInterval, true);
	}

	//! Initialize license system with Olympus prices
	protected void InitializeLicenseSystem()
	{
		// Basic licenses
		m_mLicensePrices.Set("driver", 10000);
		m_mLicensePrices.Set("boat", 5000);
		m_mLicensePrices.Set("pilot", 50000);
		m_mLicensePrices.Set("gun", 25000);
		m_mLicensePrices.Set("wpl", 50000); // Workers Protection License
		m_mLicensePrices.Set("dive", 3000);
		m_mLicensePrices.Set("truck", 20000);

		// Processing licenses
		m_mLicensePrices.Set("oil", 10000);
		m_mLicensePrices.Set("diamond", 35000);
		m_mLicensePrices.Set("salt", 12000);
		m_mLicensePrices.Set("sand", 14500);
		m_mLicensePrices.Set("iron", 9500);
		m_mLicensePrices.Set("copper", 8000);
		m_mLicensePrices.Set("cement", 6500);
		m_mLicensePrices.Set("platinum", 10000);
		m_mLicensePrices.Set("silver", 9000);

		// Special licenses
		m_mLicensePrices.Set("rebel", 75000);
		m_mLicensePrices.Set("vigilante", 60000);
		m_mLicensePrices.Set("home", 100000);

		// Illegal processing licenses
		m_mLicensePrices.Set("heroin", 25000);
		m_mLicensePrices.Set("marijuana", 17500);
		m_mLicensePrices.Set("medmarijuana", 1500);
		m_mLicensePrices.Set("cocaine", 30000);
		m_mLicensePrices.Set("frog", 24000);
		m_mLicensePrices.Set("crystalmeth", 55000);
		m_mLicensePrices.Set("moonshine", 54000);
		m_mLicensePrices.Set("mushroom", 25000);

		// Police licenses
		m_mLicensePrices.Set("cop_air", 0);
		m_mLicensePrices.Set("cop_cg", 0);

		// Medical licenses
		m_mLicensePrices.Set("med_air", 0);
		m_mLicensePrices.Set("med_cg", 0);

		// Build available licenses array
		foreach (string licenseType, int price : m_mLicensePrices)
		{
			m_aAvailableLicenses.Insert(licenseType);
		}

		Print(string.Format("[EdenRP] Initialized %1 license types", m_aAvailableLicenses.Count()), LogLevel.NORMAL);
	}

	//! Handle player connection
	void OnPlayerConnected(string playerID, string playerName)
	{
		if (m_aConnectedPlayers.Find(playerID) == -1)
		{
			m_aConnectedPlayers.Insert(playerID);
		}

		// Load or create player data
		LoadPlayerData(playerID, playerName);

		Print(string.Format("[EdenRP] Player %1 (%2) connected", playerName, playerID), LogLevel.NORMAL);
	}

	//! Handle player disconnection
	void OnPlayerDisconnected(string playerID)
	{
		int index = m_aConnectedPlayers.Find(playerID);
		if (index != -1)
		{
			m_aConnectedPlayers.Remove(index);
		}

		// Save player data
		SavePlayerData(playerID);

		Print(string.Format("[EdenRP] Player %1 disconnected", playerID), LogLevel.NORMAL);
	}

	//! Load player data from database
	protected void LoadPlayerData(string playerID, string playerName)
	{
		// Check if player data already exists
		if (m_mPlayerData.Contains(playerID))
			return;

		// Create new player data or load from database
		ref EdenRPPlayerData playerData = new EdenRPPlayerData();
		playerData.m_sPlayerID = playerID;
		playerData.m_sPlayerName = playerName;
		playerData.m_iCash = 0;
		playerData.m_iBankAccount = ********; // Starting bank amount from Olympus
		playerData.m_eFaction = EdenRPFaction.CIVILIAN;
		playerData.m_iFactionRank = 0;
		playerData.m_vTitleColor = Vector(217, 217, 217); // Default color

		m_mPlayerData.Set(playerID, playerData);

		// TODO: Implement database loading
		// LoadPlayerDataFromDatabase(playerID, playerData);
	}

	//! Save player data to database
	protected void SavePlayerData(string playerID)
	{
		if (!m_mPlayerData.Contains(playerID))
			return;

		ref EdenRPPlayerData playerData = m_mPlayerData.Get(playerID);

		// TODO: Implement database saving
		// SavePlayerDataToDatabase(playerData);
	}

	//! Get player data
	EdenRPPlayerData GetPlayerData(string playerID)
	{
		if (m_mPlayerData.Contains(playerID))
			return m_mPlayerData.Get(playerID);

		return null;
	}

	//! Set player cash
	void SetPlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iCash = amount;
		}
	}

	//! Add cash to player
	void AddPlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iCash += amount;
		}
	}

	//! Remove cash from player
	bool RemovePlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData && playerData.m_iCash >= amount)
		{
			playerData.m_iCash -= amount;
			return true;
		}
		return false;
	}

	//! Set player bank account
	void SetPlayerBank(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iBankAccount = amount;
		}
	}

	//! Process paycheck system
	protected void ProcessPaychecks()
	{
		foreach (string playerID : m_aConnectedPlayers)
		{
			ref EdenRPPlayerData playerData = GetPlayerData(playerID);
			if (playerData)
			{
				// Add paycheck to bank account
				playerData.m_iBankAccount += m_iPaycheckAmount;

				// TODO: Send notification to player
				// NotifyPlayer(playerID, "Paycheck received: $" + m_iPaycheckAmount);
			}
		}
	}

	//! Get connected players count
	int GetConnectedPlayersCount()
	{
		return m_aConnectedPlayers.Count();
	}

	//! Get all connected player IDs
	array<string> GetConnectedPlayers()
	{
		return m_aConnectedPlayers;
	}

	//! Buy license for player
	bool BuyLicense(string playerID, string licenseType)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (!playerData)
			return false;

		if (!m_mLicensePrices.Contains(licenseType))
			return false;

		if (playerData.HasLicense(licenseType))
			return false; // Already has license

		int price = m_mLicensePrices.Get(licenseType);

		// Check if player has enough money
		if (playerData.m_iCash < price)
			return false;

		// Handle special license conflicts (from Olympus logic)
		if (licenseType == "vigilante")
		{
			playerData.RemoveLicense("rebel");
			playerData.RemoveLicense("wpl");
		}
		else if (licenseType == "rebel")
		{
			playerData.RemoveLicense("vigilante");
			playerData.RemoveLicense("wpl");
		}

		// Purchase license
		playerData.m_iCash -= price;
		playerData.AddLicense(licenseType);

		Print(string.Format("[EdenRP] Player %1 purchased license: %2 for $%3", playerData.m_sPlayerName, licenseType, price), LogLevel.NORMAL);
		return true;
	}

	//! Get license price
	int GetLicensePrice(string licenseType)
	{
		if (m_mLicensePrices.Contains(licenseType))
			return m_mLicensePrices.Get(licenseType);

		return -1;
	}

	//! Get available licenses
	array<string> GetAvailableLicenses()
	{
		return m_aAvailableLicenses;
	}

	//! Update player statistic
	void UpdatePlayerStatistic(string playerID, EdenRPPlayerStats statType, int value)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.UpdateStatistic(statType, value);
		}
	}

	//! Get player statistic
	int GetPlayerStatistic(string playerID, EdenRPPlayerStats statType)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			return playerData.GetStatistic(statType);
		}
		return 0;
	}

	//! Set player faction
	void SetPlayerFaction(string playerID, EdenRPFaction faction, int rank = 0)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_eFaction = faction;
			playerData.m_iFactionRank = rank;
		}
	}

	//! Get player faction
	EdenRPFaction GetPlayerFaction(string playerID)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			return playerData.m_eFaction;
		}
		return EdenRPFaction.CIVILIAN;
	}

	//! Set player admin level
	void SetPlayerAdminLevel(string playerID, int level)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iAdminLevel = level;
		}
	}

	//! Get player admin level
	int GetPlayerAdminLevel(string playerID)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			return playerData.m_iAdminLevel;
		}
		return 0;
	}

	//! Check if player has license
	bool PlayerHasLicense(string playerID, string licenseType)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			return playerData.HasLicense(licenseType);
		}
		return false;
	}

	//! Get player licenses
	array<string> GetPlayerLicenses(string playerID)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			return playerData.m_aLicenses;
		}
		return new array<string>();
	}

	//! Force save player data
	void ForceSavePlayerData(string playerID)
	{
		SavePlayerData(playerID);
	}

	//! Get player count by faction
	int GetPlayerCountByFaction(EdenRPFaction faction)
	{
		int count = 0;
		foreach (string playerID : m_aConnectedPlayers)
		{
			if (GetPlayerFaction(playerID) == faction)
				count++;
		}
		return count;
	}
}