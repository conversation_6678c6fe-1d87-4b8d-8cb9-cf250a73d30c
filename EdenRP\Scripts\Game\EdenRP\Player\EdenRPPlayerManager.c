//! EdenRP Player Manager
//! Manages player data, factions, licenses, and statistics
//! Converted from Olympus Altis Life player system

//! Player faction enumeration
enum EdenRPFaction
{
	CIVILIAN = 0,
	POLICE = 1,
	MEDICAL = 2,
	GANG = 3
}

//! Player data structure
class EdenRPPlayerData
{
	string m_sPlayerID;
	string m_sPlayerName;
	int m_iCash;
	int m_iBankAccount;
	EdenRPFaction m_eFaction;
	int m_iFactionRank;
	ref array<string> m_aLicenses;
	ref array<int> m_aStatistics;
	ref array<string> m_aTitles;
	string m_sCurrentTitle;
	vector m_vTitleColor;

	void EdenRPPlayerData()
	{
		m_aLicenses = new array<string>();
		m_aStatistics = new array<int>();
		m_aTitles = new array<string>();

		// Initialize statistics array (78 stats from Olympus)
		m_aStatistics.Resize(78);
		for (int i = 0; i < 78; i++)
		{
			m_aStatistics[i] = 0;
		}
	}
}

//! Player Manager for EdenRP
class EdenRPPlayerManager
{
	// Player data storage
	protected ref map<string, ref EdenRPPlayerData> m_mPlayerData;
	protected ref array<string> m_aConnectedPlayers;

	// Configuration
	protected int m_iPaycheckAmount = 450; // From Olympus configuration
	protected int m_iPaycheckInterval = 300000; // 5 minutes in milliseconds

	void EdenRPPlayerManager()
	{
		m_mPlayerData = new map<string, ref EdenRPPlayerData>();
		m_aConnectedPlayers = new array<string>();
	}

	//! Initialize the player manager
	void Initialize()
	{
		Print("[EdenRP] Player Manager initialized", LogLevel.NORMAL);

		// Start paycheck system
		GetGame().GetCallqueue().CallLater(ProcessPaychecks, m_iPaycheckInterval, true);
	}

	//! Handle player connection
	void OnPlayerConnected(string playerID, string playerName)
	{
		if (m_aConnectedPlayers.Find(playerID) == -1)
		{
			m_aConnectedPlayers.Insert(playerID);
		}

		// Load or create player data
		LoadPlayerData(playerID, playerName);

		Print(string.Format("[EdenRP] Player %1 (%2) connected", playerName, playerID), LogLevel.NORMAL);
	}

	//! Handle player disconnection
	void OnPlayerDisconnected(string playerID)
	{
		int index = m_aConnectedPlayers.Find(playerID);
		if (index != -1)
		{
			m_aConnectedPlayers.Remove(index);
		}

		// Save player data
		SavePlayerData(playerID);

		Print(string.Format("[EdenRP] Player %1 disconnected", playerID), LogLevel.NORMAL);
	}

	//! Load player data from database
	protected void LoadPlayerData(string playerID, string playerName)
	{
		// Check if player data already exists
		if (m_mPlayerData.Contains(playerID))
			return;

		// Create new player data or load from database
		ref EdenRPPlayerData playerData = new EdenRPPlayerData();
		playerData.m_sPlayerID = playerID;
		playerData.m_sPlayerName = playerName;
		playerData.m_iCash = 0;
		playerData.m_iBankAccount = ********; // Starting bank amount from Olympus
		playerData.m_eFaction = EdenRPFaction.CIVILIAN;
		playerData.m_iFactionRank = 0;
		playerData.m_vTitleColor = Vector(217, 217, 217); // Default color

		m_mPlayerData.Set(playerID, playerData);

		// TODO: Implement database loading
		// LoadPlayerDataFromDatabase(playerID, playerData);
	}

	//! Save player data to database
	protected void SavePlayerData(string playerID)
	{
		if (!m_mPlayerData.Contains(playerID))
			return;

		ref EdenRPPlayerData playerData = m_mPlayerData.Get(playerID);

		// TODO: Implement database saving
		// SavePlayerDataToDatabase(playerData);
	}

	//! Get player data
	EdenRPPlayerData GetPlayerData(string playerID)
	{
		if (m_mPlayerData.Contains(playerID))
			return m_mPlayerData.Get(playerID);

		return null;
	}

	//! Set player cash
	void SetPlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iCash = amount;
		}
	}

	//! Add cash to player
	void AddPlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iCash += amount;
		}
	}

	//! Remove cash from player
	bool RemovePlayerCash(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData && playerData.m_iCash >= amount)
		{
			playerData.m_iCash -= amount;
			return true;
		}
		return false;
	}

	//! Set player bank account
	void SetPlayerBank(string playerID, int amount)
	{
		ref EdenRPPlayerData playerData = GetPlayerData(playerID);
		if (playerData)
		{
			playerData.m_iBankAccount = amount;
		}
	}

	//! Process paycheck system
	protected void ProcessPaychecks()
	{
		foreach (string playerID : m_aConnectedPlayers)
		{
			ref EdenRPPlayerData playerData = GetPlayerData(playerID);
			if (playerData)
			{
				// Add paycheck to bank account
				playerData.m_iBankAccount += m_iPaycheckAmount;

				// TODO: Send notification to player
				// NotifyPlayer(playerID, "Paycheck received: $" + m_iPaycheckAmount);
			}
		}
	}

	//! Get connected players count
	int GetConnectedPlayersCount()
	{
		return m_aConnectedPlayers.Count();
	}

	//! Get all connected player IDs
	array<string> GetConnectedPlayers()
	{
		return m_aConnectedPlayers;
	}
}