//! EdenRP Vehicle Manager
//! Manages vehicle ownership, garages, and vehicle systems
//! Converted from Olympus Altis Life vehicle system

//! Vehicle data structure
class EdenRPVehicleData
{
	string m_sVehicleId;
	string m_sOwnerId;
	string m_sClassName;
	string m_sVehicleType; // "Car", "Air", "Ship"
	string m_sSide; // "civ", "cop", "med"
	bool m_bIsAlive;
	bool m_bIsActive;
	string m_sPlate;
	ref array<int> m_aColor;
	int m_iInsurance; // 0=none, 1=basic, 2=full
	ref array<int> m_aModifications;
	string m_sInventory;
	bool m_bIsImpounded;
	float m_fImpoundTime;
	int m_iGangId;
	vector m_vLastPosition;
	float m_fLastSeen;

	void EdenRPVehicleData()
	{
		m_aColor = new array<int>();
		m_aModifications = new array<int>();
		m_sInventory = "[]";
		m_bIsImpounded = false;
		m_fImpoundTime = 0.0;
		m_iGangId = 0;
		m_vLastPosition = "0 0 0";
		m_fLastSeen = 0.0;

		// Default modifications [turbo, armor, security, upgrade1]
		m_aModifications = {0, 0, 0, 0};

		// Default color [colorIndex, finish]
		m_aColor = {0, 0};
	}

	//! Calculate sell price based on Olympus formula
	int GetSellPrice()
	{
		// Get base vehicle price from configuration
		int basePrice = GetVehicleBasePrice(m_sClassName);

		// Apply depreciation (75% of base price)
		float sellPercentage = 0.75;

		// Insurance affects sell price
		switch (m_iInsurance)
		{
			case 1: sellPercentage += 0.05; // Basic insurance +5%
			case 2: sellPercentage += 0.10; // Full insurance +10%
		}

		// Modifications add value
		int modValue = 0;
		for (int i = 0; i < m_aModifications.Count(); i++)
		{
			modValue += m_aModifications[i] * 1000; // Each mod tier adds $1000
		}

		return (int)(basePrice * sellPercentage) + modValue;
	}

	//! Calculate retrieval price
	int GetRetrievalPrice()
	{
		// Base retrieval cost
		int baseCost = 500;

		// Type-based cost
		switch (m_sVehicleType)
		{
			case "Air": baseCost = 2000;
			case "Ship": baseCost = 1000;
		}

		// Insurance reduces retrieval cost
		switch (m_iInsurance)
		{
			case 1: baseCost = (int)(baseCost * 0.8); // Basic insurance -20%
			case 2: baseCost = (int)(baseCost * 0.5); // Full insurance -50%
		}

		return baseCost;
	}

	//! Get vehicle base price (placeholder - should be from config)
	protected int GetVehicleBasePrice(string className)
	{
		// This would normally come from vehicle configuration
		// Placeholder values based on common Olympus vehicles
		switch (className)
		{
			case "C_Offroad_01_F": return 12500;
			case "C_SUV_01_F": return 15000;
			case "C_Hatchback_01_F": return 9500;
			case "B_Quadbike_01_F": return 2500;
			case "C_Van_01_transport_F": return 25000;
			case "I_Heli_light_03_unarmed_F": return 75000;
			case "C_Boat_Civil_01_F": return 6500;
			default: return 10000;
		}
	}
}

//! Garage location data
class EdenRPGarageLocation
{
	string m_sGarageId;
	string m_sGarageName;
	vector m_vPosition;
	vector m_vSpawnPosition;
	float m_fSpawnDirection;
	string m_sVehicleType; // "Car", "Air", "Ship"
	bool m_bIsActive;

	void EdenRPGarageLocation(string garageId, string garageName, vector position, string vehicleType)
	{
		m_sGarageId = garageId;
		m_sGarageName = garageName;
		m_vPosition = position;
		m_sVehicleType = vehicleType;
		m_bIsActive = true;
		m_vSpawnPosition = position;
		m_fSpawnDirection = 0.0;
	}
}

//! Vehicle shop data
class EdenRPVehicleShop
{
	string m_sShopId;
	string m_sShopName;
	vector m_vPosition;
	string m_sVehicleType;
	string m_sSide;
	ref array<string> m_aAvailableVehicles;
	bool m_bIsActive;

	void EdenRPVehicleShop(string shopId, string shopName, vector position, string vehicleType, string side)
	{
		m_sShopId = shopId;
		m_sShopName = shopName;
		m_vPosition = position;
		m_sVehicleType = vehicleType;
		m_sSide = side;
		m_aAvailableVehicles = new array<string>();
		m_bIsActive = true;
	}
}

class EdenRPVehicleManager
{
	// Vehicle storage
	protected ref map<string, ref EdenRPVehicleData> m_mPlayerVehicles;
	protected ref map<string, ref EdenRPVehicleData> m_mSpawnedVehicles;
	protected ref array<ref EdenRPVehicleData> m_aImpoundedVehicles;

	// Garage system
	protected ref map<string, ref EdenRPGarageLocation> m_mGarageLocations;
	protected ref map<string, ref EdenRPVehicleShop> m_mVehicleShops;

	// Configuration
	protected int m_iMaxVehiclesNormal = 75;
	protected int m_iMaxVehiclesRestricted = 15;
	protected int m_iImpoundFee = 5000;
	protected int m_iImpoundDuration = 1800; // 30 minutes

	// Vehicle limits by type
	protected ref map<string, int> m_mVehicleLimits;

	void EdenRPVehicleManager()
	{
		m_mPlayerVehicles = new map<string, ref EdenRPVehicleData>();
		m_mSpawnedVehicles = new map<string, ref EdenRPVehicleData>();
		m_aImpoundedVehicles = new array<ref EdenRPVehicleData>();
		m_mGarageLocations = new map<string, ref EdenRPGarageLocation>();
		m_mVehicleShops = new map<string, ref EdenRPVehicleShop>();
		m_mVehicleLimits = new map<string, int>();
	}

	//! Initialize the vehicle manager
	void Initialize()
	{
		Print("[EdenRP] Vehicle Manager initialized", LogLevel.NORMAL);

		// Initialize garage locations
		InitializeGarageLocations();

		// Initialize vehicle shops
		InitializeVehicleShops();

		// Initialize vehicle limits
		InitializeVehicleLimits();

		// Start impound cleanup timer
		GetGame().GetCallqueue().CallLater(ProcessImpoundCleanup, 60000, true); // Every minute
	}

	//! Initialize garage locations based on Olympus system
	protected void InitializeGarageLocations()
	{
		// Civilian garages
		AddGarageLocation("kavala_garage", "Kavala Garage", "3664.07 0 13109.5", "Car");
		AddGarageLocation("pyrgos_garage", "Pyrgos Garage", "14947.4 0 12766.9", "Car");
		AddGarageLocation("athira_garage", "Athira Garage", "14139.5 0 18943.9", "Car");
		AddGarageLocation("sofia_garage", "Sofia Garage", "25977.4 0 21706.1", "Car");
		AddGarageLocation("neochori_garage", "Neochori Garage", "13458.8 0 14679.5", "Car");

		// Air garages
		AddGarageLocation("kavala_air", "Kavala Airport", "3245.99 0 12841.2", "Air");
		AddGarageLocation("pyrgos_air", "Pyrgos Airport", "16849.4 0 12615.9", "Air");
		AddGarageLocation("athira_air", "Athira Airport", "13945.1 0 19808.5", "Air");

		// Boat garages
		AddGarageLocation("kavala_boat", "Kavala Harbor", "3866.15 0 11641.8", "Ship");
		AddGarageLocation("pyrgos_boat", "Pyrgos Harbor", "14681.2 0 11501.6", "Ship");
		AddGarageLocation("athira_boat", "Athira Harbor", "13990.8 0 18206.4", "Ship");

		Print(string.Format("[EdenRP] Initialized %1 garage locations", m_mGarageLocations.Count()), LogLevel.NORMAL);
	}

	//! Add garage location
	protected void AddGarageLocation(string garageId, string garageName, string position, string vehicleType)
	{
		vector pos = position.ToVector();
		ref EdenRPGarageLocation garage = new EdenRPGarageLocation(garageId, garageName, pos, vehicleType);
		m_mGarageLocations.Set(garageId, garage);
	}

	//! Initialize vehicle shops
	protected void InitializeVehicleShops()
	{
		// Civilian vehicle shops
		AddVehicleShop("kavala_car_shop", "Kavala Car Shop", "3644.24 0 12929.5", "Car", "civ");
		AddVehicleShop("pyrgos_car_shop", "Pyrgos Car Shop", "14839.1 0 16727.8", "Car", "civ");
		AddVehicleShop("athira_car_shop", "Athira Car Shop", "14207.6 0 18890.9", "Car", "civ");

		// Aircraft dealers
		AddVehicleShop("kavala_air_shop", "Kavala Aircraft Dealer", "3118.68 0 12841.4", "Air", "civ");
		AddVehicleShop("pyrgos_air_shop", "Pyrgos Aircraft Dealer", "16673.5 0 12615.4", "Air", "civ");

		// Boat shops
		AddVehicleShop("kavala_boat_shop", "Kavala Boat Shop", "3729.96 0 11624.6", "Ship", "civ");
		AddVehicleShop("pyrgos_boat_shop", "Pyrgos Boat Shop", "14587.9 0 11501.2", "Ship", "civ");

		Print(string.Format("[EdenRP] Initialized %1 vehicle shops", m_mVehicleShops.Count()), LogLevel.NORMAL);
	}

	//! Add vehicle shop
	protected void AddVehicleShop(string shopId, string shopName, string position, string vehicleType, string side)
	{
		vector pos = position.ToVector();
		ref EdenRPVehicleShop shop = new EdenRPVehicleShop(shopId, shopName, pos, vehicleType, side);
		m_mVehicleShops.Set(shopId, shop);
	}

	//! Initialize vehicle limits
	protected void InitializeVehicleLimits()
	{
		m_mVehicleLimits.Set("Car", 50);
		m_mVehicleLimits.Set("Air", 15);
		m_mVehicleLimits.Set("Ship", 10);

		Print("[EdenRP] Vehicle limits initialized", LogLevel.NORMAL);
	}

	//! Get player vehicle count
	int GetPlayerVehicleCount(string playerID)
	{
		int count = 0;
		foreach (string vehicleId, EdenRPVehicleData vehicleData : m_mPlayerVehicles)
		{
			if (vehicleData.m_sOwnerId == playerID && vehicleData.m_bIsAlive)
			{
				count++;
			}
		}
		return count;
	}

	//! Get player vehicle count by type
	int GetPlayerVehicleCountByType(string playerID, string vehicleType)
	{
		int count = 0;
		foreach (string vehicleId, EdenRPVehicleData vehicleData : m_mPlayerVehicles)
		{
			if (vehicleData.m_sOwnerId == playerID && vehicleData.m_sVehicleType == vehicleType && vehicleData.m_bIsAlive)
			{
				count++;
			}
		}
		return count;
	}

	//! Check if player can buy vehicle
	bool CanPlayerBuyVehicle(string playerID, string vehicleType)
	{
		// Get player manager to check restrictions
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		ref EdenRPPlayerData playerData = playerManager.GetPlayerData(playerID);
		if (!playerData)
			return false;

		// Check total vehicle limit
		int totalVehicles = GetPlayerVehicleCount(playerID);
		int maxVehicles = playerData.m_bRestricted ? m_iMaxVehiclesRestricted : m_iMaxVehiclesNormal;

		if (totalVehicles >= maxVehicles)
			return false;

		// Check type-specific limit
		if (m_mVehicleLimits.Contains(vehicleType))
		{
			int typeCount = GetPlayerVehicleCountByType(playerID, vehicleType);
			int typeLimit = m_mVehicleLimits.Get(vehicleType);

			if (typeCount >= typeLimit)
				return false;
		}

		return true;
	}

	//! Purchase vehicle
	bool PurchaseVehicle(string playerID, string className, string vehicleType, string side, int price, array<int> color)
	{
		if (!CanPlayerBuyVehicle(playerID, vehicleType))
			return false;

		// Get player manager to process payment
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		// Check if player has enough money
		if (!playerManager.RemovePlayerCash(playerID, price))
			return false;

		// Generate vehicle ID and plate
		string vehicleId = GenerateVehicleId();
		string plate = GeneratePlate();

		// Create vehicle data
		ref EdenRPVehicleData vehicleData = new EdenRPVehicleData();
		vehicleData.m_sVehicleId = vehicleId;
		vehicleData.m_sOwnerId = playerID;
		vehicleData.m_sClassName = className;
		vehicleData.m_sVehicleType = vehicleType;
		vehicleData.m_sSide = side;
		vehicleData.m_bIsAlive = true;
		vehicleData.m_bIsActive = false; // In garage
		vehicleData.m_sPlate = plate;
		vehicleData.m_aColor = color;
		vehicleData.m_iInsurance = 0; // No insurance by default

		// Store vehicle
		m_mPlayerVehicles.Set(vehicleId, vehicleData);

		// Update statistics
		playerManager.UpdatePlayerStatistic(playerID, EdenRPPlayerStats.MONEY_SPENT, price);

		Print(string.Format("[EdenRP] Player %1 purchased vehicle %2 (%3) for $%4", playerID, className, vehicleId, price), LogLevel.NORMAL);
		return true;
	}

	//! Sell vehicle
	bool SellVehicle(string playerID, string vehicleId)
	{
		if (!m_mPlayerVehicles.Contains(vehicleId))
			return false;

		ref EdenRPVehicleData vehicleData = m_mPlayerVehicles.Get(vehicleId);
		if (vehicleData.m_sOwnerId != playerID)
			return false;

		// Calculate sell price
		int sellPrice = vehicleData.GetSellPrice();

		// Get player manager to add money
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		// Add money to bank account
		playerManager.SetPlayerBank(playerID, playerManager.GetPlayerData(playerID).m_iBankAccount + sellPrice);

		// Remove vehicle
		m_mPlayerVehicles.Remove(vehicleId);

		// Remove from spawned vehicles if active
		if (m_mSpawnedVehicles.Contains(vehicleId))
		{
			m_mSpawnedVehicles.Remove(vehicleId);
		}

		Print(string.Format("[EdenRP] Player %1 sold vehicle %2 for $%3", playerID, vehicleId, sellPrice), LogLevel.NORMAL);
		return true;
	}

	//! Retrieve vehicle from garage
	bool RetrieveVehicle(string playerID, string vehicleId, string garageId)
	{
		if (!m_mPlayerVehicles.Contains(vehicleId) || !m_mGarageLocations.Contains(garageId))
			return false;

		ref EdenRPVehicleData vehicleData = m_mPlayerVehicles.Get(vehicleId);
		if (vehicleData.m_sOwnerId != playerID || vehicleData.m_bIsActive)
			return false; // Not owner or already spawned

		// Check if vehicle is impounded
		if (vehicleData.m_bIsImpounded)
			return false;

		// Calculate retrieval price
		int retrievalPrice = vehicleData.GetRetrievalPrice();

		// Get player manager to process payment
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		// Check if player has enough money
		if (!playerManager.RemovePlayerCash(playerID, retrievalPrice))
			return false;

		// Mark as active and add to spawned vehicles
		vehicleData.m_bIsActive = true;
		m_mSpawnedVehicles.Set(vehicleId, vehicleData);

		// TODO: Actually spawn the vehicle entity at garage location
		// This would require integration with Reforger's vehicle spawning system

		Print(string.Format("[EdenRP] Player %1 retrieved vehicle %2 from %3 for $%4", playerID, vehicleId, garageId, retrievalPrice), LogLevel.NORMAL);
		return true;
	}

	//! Store vehicle in garage
	bool StoreVehicle(string playerID, string vehicleId)
	{
		if (!m_mSpawnedVehicles.Contains(vehicleId))
			return false;

		ref EdenRPVehicleData vehicleData = m_mSpawnedVehicles.Get(vehicleId);
		if (vehicleData.m_sOwnerId != playerID)
			return false;

		// Mark as inactive and remove from spawned vehicles
		vehicleData.m_bIsActive = false;
		m_mSpawnedVehicles.Remove(vehicleId);

		// TODO: Actually despawn the vehicle entity
		// This would require integration with Reforger's vehicle system

		Print(string.Format("[EdenRP] Player %1 stored vehicle %2", playerID, vehicleId), LogLevel.NORMAL);
		return true;
	}

	//! Impound vehicle
	bool ImpoundVehicle(string vehicleId, string impoundReason = "")
	{
		if (!m_mSpawnedVehicles.Contains(vehicleId))
			return false;

		ref EdenRPVehicleData vehicleData = m_mSpawnedVehicles.Get(vehicleId);

		// Mark as impounded
		vehicleData.m_bIsImpounded = true;
		vehicleData.m_fImpoundTime = GetGame().GetWorld().GetWorldTime();
		vehicleData.m_bIsActive = false;

		// Move to impounded vehicles
		m_aImpoundedVehicles.Insert(vehicleData);
		m_mSpawnedVehicles.Remove(vehicleId);

		Print(string.Format("[EdenRP] Vehicle %1 impounded. Reason: %2", vehicleId, impoundReason), LogLevel.NORMAL);
		return true;
	}

	//! Pay impound fee and retrieve vehicle
	bool PayImpoundFee(string playerID, string vehicleId)
	{
		// Find impounded vehicle
		ref EdenRPVehicleData vehicleData = null;
		int impoundIndex = -1;

		for (int i = 0; i < m_aImpoundedVehicles.Count(); i++)
		{
			if (m_aImpoundedVehicles[i].m_sVehicleId == vehicleId && m_aImpoundedVehicles[i].m_sOwnerId == playerID)
			{
				vehicleData = m_aImpoundedVehicles[i];
				impoundIndex = i;
				break;
			}
		}

		if (!vehicleData)
			return false;

		// Get player manager to process payment
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		// Check if player has enough money
		if (!playerManager.RemovePlayerCash(playerID, m_iImpoundFee))
			return false;

		// Remove from impound
		vehicleData.m_bIsImpounded = false;
		vehicleData.m_fImpoundTime = 0.0;
		m_aImpoundedVehicles.Remove(impoundIndex);

		Print(string.Format("[EdenRP] Player %1 paid impound fee $%2 for vehicle %3", playerID, m_iImpoundFee, vehicleId), LogLevel.NORMAL);
		return true;
	}

	//! Process impound cleanup (remove expired impounds)
	protected void ProcessImpoundCleanup()
	{
		float currentTime = GetGame().GetWorld().GetWorldTime();

		for (int i = m_aImpoundedVehicles.Count() - 1; i >= 0; i--)
		{
			ref EdenRPVehicleData vehicleData = m_aImpoundedVehicles[i];

			// Check if impound has expired
			if (currentTime - vehicleData.m_fImpoundTime >= m_iImpoundDuration)
			{
				// Remove expired impounded vehicle
				m_mPlayerVehicles.Remove(vehicleData.m_sVehicleId);
				m_aImpoundedVehicles.Remove(i);

				Print(string.Format("[EdenRP] Expired impounded vehicle %1 removed", vehicleData.m_sVehicleId), LogLevel.NORMAL);
			}
		}
	}

	//! Generate unique vehicle ID
	protected string GenerateVehicleId()
	{
		// Simple ID generation - in production this should be more robust
		int timestamp = (int)GetGame().GetWorld().GetWorldTime();
		int random = Math.RandomInt(1000, 9999);
		return string.Format("VEH_%1_%2", timestamp, random);
	}

	//! Generate vehicle plate
	protected string GeneratePlate()
	{
		// Generate random 6-digit plate
		return string.Format("%1", Math.RandomInt(100000, 999999));
	}

	//! Get player vehicles
	array<ref EdenRPVehicleData> GetPlayerVehicles(string playerID)
	{
		ref array<ref EdenRPVehicleData> playerVehicles = new array<ref EdenRPVehicleData>();

		foreach (string vehicleId, EdenRPVehicleData vehicleData : m_mPlayerVehicles)
		{
			if (vehicleData.m_sOwnerId == playerID && vehicleData.m_bIsAlive)
			{
				playerVehicles.Insert(vehicleData);
			}
		}

		return playerVehicles;
	}

	//! Get player vehicles by type
	array<ref EdenRPVehicleData> GetPlayerVehiclesByType(string playerID, string vehicleType)
	{
		ref array<ref EdenRPVehicleData> playerVehicles = new array<ref EdenRPVehicleData>();

		foreach (string vehicleId, EdenRPVehicleData vehicleData : m_mPlayerVehicles)
		{
			if (vehicleData.m_sOwnerId == playerID && vehicleData.m_sVehicleType == vehicleType && vehicleData.m_bIsAlive)
			{
				playerVehicles.Insert(vehicleData);
			}
		}

		return playerVehicles;
	}

	//! Get impounded vehicles for player
	array<ref EdenRPVehicleData> GetPlayerImpoundedVehicles(string playerID)
	{
		ref array<ref EdenRPVehicleData> impoundedVehicles = new array<ref EdenRPVehicleData>();

		foreach (EdenRPVehicleData vehicleData : m_aImpoundedVehicles)
		{
			if (vehicleData.m_sOwnerId == playerID)
			{
				impoundedVehicles.Insert(vehicleData);
			}
		}

		return impoundedVehicles;
	}

	//! Get garage locations
	map<string, ref EdenRPGarageLocation> GetGarageLocations()
	{
		return m_mGarageLocations;
	}

	//! Get garage locations by type
	array<ref EdenRPGarageLocation> GetGarageLocationsByType(string vehicleType)
	{
		ref array<ref EdenRPGarageLocation> garages = new array<ref EdenRPGarageLocation>();

		foreach (string garageId, EdenRPGarageLocation garage : m_mGarageLocations)
		{
			if (garage.m_sVehicleType == vehicleType && garage.m_bIsActive)
			{
				garages.Insert(garage);
			}
		}

		return garages;
	}

	//! Get vehicle shops
	map<string, ref EdenRPVehicleShop> GetVehicleShops()
	{
		return m_mVehicleShops;
	}

	//! Get vehicle shops by type and side
	array<ref EdenRPVehicleShop> GetVehicleShopsByTypeAndSide(string vehicleType, string side)
	{
		ref array<ref EdenRPVehicleShop> shops = new array<ref EdenRPVehicleShop>();

		foreach (string shopId, EdenRPVehicleShop shop : m_mVehicleShops)
		{
			if (shop.m_sVehicleType == vehicleType && shop.m_sSide == side && shop.m_bIsActive)
			{
				shops.Insert(shop);
			}
		}

		return shops;
	}

	//! Get total vehicle statistics
	map<string, int> GetVehicleStatistics()
	{
		ref map<string, int> stats = new map<string, int>();

		int totalVehicles = m_mPlayerVehicles.Count();
		int activeVehicles = m_mSpawnedVehicles.Count();
		int impoundedVehicles = m_aImpoundedVehicles.Count();

		stats.Set("totalVehicles", totalVehicles);
		stats.Set("activeVehicles", activeVehicles);
		stats.Set("impoundedVehicles", impoundedVehicles);
		stats.Set("garageLocations", m_mGarageLocations.Count());
		stats.Set("vehicleShops", m_mVehicleShops.Count());

		return stats;
	}
}