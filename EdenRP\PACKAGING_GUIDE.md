# EdenRP Mod Packaging Guide

## Overview
This guide covers how to properly package the EdenRP mod for Arma Reforger distribution via Steam Workshop or direct download.

## Prerequisites

### Required Software
- **Arma Reforger Tools** (Workbench) - Latest version
- **Arma Reforger** - Game client for testing
- **Steam Account** - For Workshop publishing (if using Workshop)

### Project Structure Verification
Ensure your EdenRP folder has the correct structure:

```
EdenRP/
├── addon.gproj                    # Main project file
├── mod.conf                       # Mod configuration
├── meta/
│   └── meta.conf                  # Meta configuration
├── resourceDatabase/
│   └── resourceDatabase.rdb       # Resource database (auto-generated)
├── Scripts/Game/EdenRP/           # All script files
├── Configs/                       # Configuration files
├── Prefabs/                       # Game object prefabs (if any)
├── UI/                           # User interface files (if any)
├── Worlds/                       # World modifications (if any)
└── Documentation files
```

## Packaging Process

### Step 1: Open Workbench
1. Launch **Arma Reforger Tools** (Workbench)
2. Click **"Open Project"**
3. Navigate to your EdenRP folder
4. Select **addon.gproj** and open

### Step 2: Verify Project Settings
1. In Workbench, check the **Project Settings**:
   - **GUID**: `{E8F4C2A3-B7D1-4F2E-9A5C-1D3E7F9B2C4A}`
   - **Name**: EdenRP
   - **Version**: 1.0.0
   - **Dependencies**: ArmaReforger

### Step 3: Build Resource Database
1. In Workbench menu: **Build → Build Resource Database**
2. Wait for the build process to complete
3. Check for any errors in the output log
4. The `resourceDatabase.rdb` file will be updated automatically

### Step 4: Validate Scripts
1. In Workbench menu: **Build → Validate Scripts**
2. Check the **Script Editor** for any compilation errors
3. Fix any syntax errors or missing dependencies
4. Ensure all EdenRP manager classes compile successfully

### Step 5: Test Locally
1. In Workbench menu: **Play → Launch Game**
2. Create a test scenario with EdenRP enabled
3. Verify core systems initialize properly:
   ```
   [EdenRP] Core Manager initialized successfully
   [EdenRP] Player Manager initialized
   [EdenRP] Economy Manager initialized
   [EdenRP] Vehicle Manager initialized
   [EdenRP] Housing Manager initialized
   [EdenRP] Gang Manager initialized
   [EdenRP] Law Manager initialized
   [EdenRP] Medical Manager initialized
   ```

## Distribution Methods

### Method 1: Steam Workshop (Recommended)

#### Prerequisites
- Steam account with Arma Reforger ownership
- Workshop publishing permissions

#### Publishing Steps
1. In Workbench menu: **Publish → Publish to Workshop**
2. Fill in Workshop details:
   - **Title**: EdenRP - Complete Olympus Altis Life Conversion
   - **Description**: Use the description from README.md
   - **Tags**: Roleplay, Economy, Police, Gangs, Civilian
   - **Visibility**: Public (or Friends Only for testing)
3. Upload preview images and screenshots
4. Click **"Publish"**
5. Wait for upload to complete
6. Note the **Workshop ID** for server configuration

#### Workshop Item Management
- **Updates**: Use "Update Workshop Item" in Workbench
- **Version Control**: Update version number in addon.gproj
- **Change Notes**: Document changes for each update

### Method 2: Direct Distribution

#### Creating Distribution Package
1. **Copy EdenRP folder** to a clean directory
2. **Remove development files**:
   - Remove `.git` folders (if using version control)
   - Remove temporary build files
   - Keep only essential files for distribution
3. **Create ZIP archive**:
   - Name: `EdenRP_v1.0.0.zip`
   - Include installation instructions
4. **Upload to file hosting** (Google Drive, Dropbox, etc.)

#### Installation Instructions for Users
```
1. Download EdenRP_v1.0.0.zip
2. Extract to: %USERPROFILE%\Documents\My Games\ArmaReforger\addons\
3. Ensure folder structure is: addons\EdenRP\addon.gproj
4. Launch Arma Reforger
5. Enable EdenRP in the Addons menu
```

## Server Configuration

### For Workshop Distribution
Add to server configuration:
```json
{
  "mods": [
    {
      "modId": "WORKSHOP_ID_HERE",
      "name": "EdenRP"
    }
  ]
}
```

### For Direct Distribution
1. Copy EdenRP folder to server addons directory
2. Add to server startup parameters:
```bash
-addons EdenRP
```

## Quality Assurance

### Pre-Release Checklist
- [ ] All scripts compile without errors
- [ ] Resource database builds successfully
- [ ] Core systems initialize properly
- [ ] No missing dependencies
- [ ] Version numbers are consistent
- [ ] Documentation is up to date
- [ ] Test on clean Arma Reforger installation

### Testing Scenarios
1. **Single Player Test**:
   - Load EdenRP in single player
   - Verify systems initialize
   - Test basic functionality

2. **Multiplayer Test**:
   - Host dedicated server with EdenRP
   - Connect multiple clients
   - Test player data persistence
   - Verify economy system synchronization

3. **Performance Test**:
   - Monitor server performance
   - Check memory usage
   - Verify timer systems don't cause lag

## Troubleshooting

### Common Packaging Issues

#### Script Compilation Errors
- **Issue**: Scripts fail to compile
- **Solution**: Check syntax, missing semicolons, bracket matching
- **Tools**: Use Workbench Script Editor for debugging

#### Resource Database Build Fails
- **Issue**: Build process stops with errors
- **Solution**: Check file paths, missing resources, corrupted files
- **Fix**: Clean build, verify all referenced files exist

#### Mod Doesn't Load in Game
- **Issue**: EdenRP not visible in addons menu
- **Solution**: Verify addon.gproj structure, check GUID uniqueness
- **Check**: Ensure mod.conf has correct format

#### Workshop Upload Fails
- **Issue**: Upload process fails or times out
- **Solution**: Check internet connection, file size limits
- **Retry**: Use "Update Workshop Item" instead of new upload

### Debug Mode
Enable debug logging in EdenRPConfig.conf:
```
EdenRPConfig {
    DebugMode 1
    VerboseLogging 1
}
```

## Version Management

### Version Numbering
Use semantic versioning: `MAJOR.MINOR.PATCH`
- **MAJOR**: Breaking changes, major feature additions
- **MINOR**: New features, backward compatible
- **PATCH**: Bug fixes, small improvements

### Update Process
1. Update version in `addon.gproj`
2. Update version in `mod.conf`
3. Update version in `meta/meta.conf`
4. Document changes in changelog
5. Test thoroughly before release
6. Publish update to Workshop/distribution

## Support and Maintenance

### User Support
- Monitor Workshop comments and ratings
- Respond to bug reports promptly
- Provide clear installation instructions
- Maintain compatibility with Arma Reforger updates

### Long-term Maintenance
- Regular compatibility checks with game updates
- Performance optimization based on user feedback
- Feature additions based on community requests
- Security updates for anti-cheat systems

## Conclusion

Proper packaging is crucial for EdenRP's success. Following this guide ensures:
- Professional distribution quality
- Easy installation for users
- Reliable server deployment
- Maintainable update process

The EdenRP mod is now ready for packaging and distribution to the Arma Reforger community!