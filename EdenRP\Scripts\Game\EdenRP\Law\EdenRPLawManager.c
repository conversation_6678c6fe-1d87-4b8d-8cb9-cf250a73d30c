//! EdenRP Law Manager
//! Manages law enforcement, arrests, and wanted system
//! Converted from Olympus Altis Life law enforcement system

//! Police rank enumeration (from Olympus system)
enum EdenRPPoliceRank
{
	DEPUTY = 1,
	PATROL_OFFICER = 2,
	CORPORAL = 3,
	RETIRED_SRAPD = 4,
	STAFF_SERGEANT = 5,
	SERGEANT = 6,
	LIEUTENANT = 7,
	STAFF_CHIEF = 8,
	DEPUTY_CHIEF = 9,
	CHIEF_OF_POLICE = 10
}

//! Crime types enumeration (from Olympus 75 crimes)
enum EdenRPCrimeType
{
	SPEEDING = 0, RECKLESS_DRIVING = 1, VEHICULAR_MANSLAUGHTER = 2, HIT_AND_RUN = 3,
	DRIVING_UNREGISTERED = 4, DRIVING_STOLEN = 5, DRIVING_IMPAIRED = 6, FAILURE_TO_STOP = 7,
	ILLEGAL_PARKING = 8, DRIVING_UNLICENSED = 9, CHOPPING_VEHICLES = 10, LOCKPICKING = 11,
	POSSESSION_ILLEGAL_ITEMS = 12, POSSESSION_EXPLOSIVES = 13, MONEY_LAUNDERING = 14,
	EXCEEDING_SPEED_LIMIT = 15, ILLEGAL_WEAPONS = 16, MANSLAUGHTER = 17, MURDER = 18,
	ATTEMPTED_MURDER = 19, ASSAULT = 20, BATTERY = 21, KIDNAPPING = 22, HOSTAGE_TAKING = 23,
	ROBBERY = 24, ARMED_ROBBERY = 25, BANK_ROBBERY = 26, FEDERAL_RESERVE_ROBBERY = 27,
	JAIL_BREAK = 28, BLACKWATER_ROBBERY = 29, PHARMACY_ROBBERY = 30, GAS_STATION_ROBBERY = 31,
	SHOP_ROBBERY = 32, HOUSE_ROBBERY = 33, DRUG_TRAFFICKING = 34, DRUG_POSSESSION = 35,
	DRUG_MANUFACTURING = 36, ILLEGAL_PROCESSING = 37, CONTRABAND = 38, TERRORISM = 39,
	SEDITION = 40, TREASON = 41, ESPIONAGE = 42, BRIBERY = 43, CORRUPTION = 44,
	OBSTRUCTION_OF_JUSTICE = 45, RESISTING_ARREST = 46, ESCAPING_CUSTODY = 47,
	INTERFERING_WITH_OFFICER = 48, IMPERSONATING_OFFICER = 49, VIGILANTE_JUSTICE = 50,
	TRESPASSING = 51, BREAKING_AND_ENTERING = 52, VANDALISM = 53, ARSON = 54,
	THEFT = 55, GRAND_THEFT = 56, IDENTITY_THEFT = 57, FRAUD = 58, FORGERY = 59,
	COUNTERFEITING = 60, TAX_EVASION = 61, RACKETEERING = 62, EXTORTION = 63,
	BLACKMAIL = 64, ATTEMPTED_BW_ROBBERY = 65, ATTEMPTED_JAIL_BREAK = 66,
	KIDNAPPING_GOVT_OFFICIAL = 67, AIDING_PHARMACY_ROBBERY = 68, POSSESSION_EXPLOSIVES_2 = 69,
	FLYING_WITHOUT_LIGHTS = 70, ATTEMPTED_BANK_ROBBERY = 71, AIDING_BANK_ROBBERY = 72,
	POSSESSION_ILLEGAL_EQUIPMENT = 73, PUBLIC_URINATION = 74, TITAN_HIT = 75
}

//! Wanted entry data structure
class EdenRPWantedEntry
{
	string m_sPlayerId;
	string m_sPlayerName;
	ref array<int> m_aCrimes; // Array of crime counts (75 crimes)
	int m_iTotalBounty;
	string m_sIssuedBy;
	float m_fIssuedTime;

	void EdenRPWantedEntry()
	{
		m_aCrimes = new array<int>();
		m_aCrimes.Resize(76); // 75 crimes + 1 for total bounty
		for (int i = 0; i < 76; i++)
		{
			m_aCrimes[i] = 0;
		}
		m_iTotalBounty = 0;
		m_fIssuedTime = 0.0;
	}

	//! Add crime to wanted entry
	void AddCrime(EdenRPCrimeType crimeType, int count = 1)
	{
		if (crimeType >= 0 && crimeType < 75)
		{
			m_aCrimes[crimeType] += count;

			// Calculate bounty based on crime type
			int bountyPerCrime = GetCrimeBounty(crimeType);
			m_iTotalBounty += bountyPerCrime * count;
		}
	}

	//! Get bounty for specific crime type
	protected int GetCrimeBounty(EdenRPCrimeType crimeType)
	{
		// Bounty values from Olympus system
		switch (crimeType)
		{
			case EdenRPCrimeType.SPEEDING: return 1500;
			case EdenRPCrimeType.RECKLESS_DRIVING: return 3500;
			case EdenRPCrimeType.VEHICULAR_MANSLAUGHTER: return 45000;
			case EdenRPCrimeType.HIT_AND_RUN: return 8500;
			case EdenRPCrimeType.MURDER: return 75000;
			case EdenRPCrimeType.ATTEMPTED_MURDER: return 50000;
			case EdenRPCrimeType.BANK_ROBBERY: return 100000;
			case EdenRPCrimeType.FEDERAL_RESERVE_ROBBERY: return 150000;
			case EdenRPCrimeType.JAIL_BREAK: return 85000;
			case EdenRPCrimeType.DRUG_TRAFFICKING: return 25000;
			case EdenRPCrimeType.DRUG_POSSESSION: return 8000;
			case EdenRPCrimeType.ILLEGAL_WEAPONS: return 15000;
			case EdenRPCrimeType.TERRORISM: return 200000;
			case EdenRPCrimeType.KIDNAPPING: return 35000;
			case EdenRPCrimeType.ROBBERY: return 12000;
			case EdenRPCrimeType.ARMED_ROBBERY: return 20000;
			default: return 5000; // Default bounty
		}
	}
}

//! Arrest data structure
class EdenRPArrestData
{
	string m_sPlayerId;
	string m_sOfficerId;
	int m_iBounty;
	float m_fJailTime; // In seconds
	float m_fStartTime;
	bool m_bIsVigilante;
	string m_sReason;

	void EdenRPArrestData()
	{
		m_iBounty = 0;
		m_fJailTime = 0.0;
		m_fStartTime = 0.0;
		m_bIsVigilante = false;
	}
}

//! Ticket data structure
class EdenRPTicketData
{
	string m_sTicketId;
	string m_sPlayerId;
	string m_sOfficerId;
	string m_sReason;
	int m_iAmount;
	float m_fIssuedTime;
	bool m_bIsPaid;

	void EdenRPTicketData()
	{
		m_iAmount = 0;
		m_fIssuedTime = 0.0;
		m_bIsPaid = false;
	}
}

class EdenRPLawManager
{
	// Wanted system
	protected ref array<ref EdenRPWantedEntry> m_aWantedList;
	protected ref map<string, ref EdenRPArrestData> m_mActiveArrests;
	protected ref array<string> m_aJailedPlayers;

	// Ticket system
	protected ref array<ref EdenRPTicketData> m_aActiveTickets;
	protected ref map<string, int> m_mTicketPrices;

	// Configuration
	protected vector m_vJailPosition = "16697.6 0 13614.7"; // From Olympus
	protected float m_fJailFeeMultiplier = 0.0048; // From Olympus
	protected int m_iMaxJailTime = 1800; // 30 minutes max
	protected int m_iMinJailTime = 60; // 1 minute min

	// Vigilante system
	protected ref map<string, int> m_mVigilanteArrests;
	protected ref map<string, int> m_mVigilanteArrestsStored;

	void EdenRPLawManager()
	{
		m_aWantedList = new array<ref EdenRPWantedEntry>();
		m_mActiveArrests = new map<string, ref EdenRPArrestData>();
		m_aJailedPlayers = new array<string>();
		m_aActiveTickets = new array<ref EdenRPTicketData>();
		m_mTicketPrices = new map<string, int>();
		m_mVigilanteArrests = new map<string, int>();
		m_mVigilanteArrestsStored = new map<string, int>();
	}

	//! Initialize the law manager
	void Initialize()
	{
		Print("[EdenRP] Law Manager initialized", LogLevel.NORMAL);

		// Initialize ticket prices
		InitializeTicketPrices();

		// Start jail timer system
		GetGame().GetCallqueue().CallLater(ProcessJailTimers, 1000, true); // Every second

		// Start ticket cleanup
		GetGame().GetCallqueue().CallLater(ProcessTicketCleanup, 300000, true); // Every 5 minutes
	}

	//! Initialize ticket prices
	protected void InitializeTicketPrices()
	{
		m_mTicketPrices.Set("speeding", 1500);
		m_mTicketPrices.Set("reckless_driving", 3500);
		m_mTicketPrices.Set("illegal_parking", 500);
		m_mTicketPrices.Set("driving_unlicensed", 2500);
		m_mTicketPrices.Set("failure_to_stop", 2000);
		m_mTicketPrices.Set("public_urination", 100);

		Print("[EdenRP] Ticket prices initialized", LogLevel.NORMAL);
	}

	//! Add player to wanted list
	void AddWantedEntry(string playerId, string playerName, EdenRPCrimeType crimeType, int count = 1, string issuedBy = "")
	{
		// Find existing wanted entry
		ref EdenRPWantedEntry wantedEntry = null;
		foreach (EdenRPWantedEntry entry : m_aWantedList)
		{
			if (entry.m_sPlayerId == playerId)
			{
				wantedEntry = entry;
				break;
			}
		}

		// Create new entry if doesn't exist
		if (!wantedEntry)
		{
			wantedEntry = new EdenRPWantedEntry();
			wantedEntry.m_sPlayerId = playerId;
			wantedEntry.m_sPlayerName = playerName;
			wantedEntry.m_sIssuedBy = issuedBy;
			wantedEntry.m_fIssuedTime = GetGame().GetWorld().GetWorldTime();
			m_aWantedList.Insert(wantedEntry);
		}

		// Add crime
		wantedEntry.AddCrime(crimeType, count);

		Print(string.Format("[EdenRP] Added wanted entry for %1: %2 (Bounty: $%3)", playerName, crimeType, wantedEntry.m_iTotalBounty), LogLevel.NORMAL);
	}

	//! Arrest player
	bool ArrestPlayer(string playerId, string officerId, bool isVigilante = false)
	{
		// Find wanted entry
		ref EdenRPWantedEntry wantedEntry = null;
		int wantedIndex = -1;

		for (int i = 0; i < m_aWantedList.Count(); i++)
		{
			if (m_aWantedList[i].m_sPlayerId == playerId)
			{
				wantedEntry = m_aWantedList[i];
				wantedIndex = i;
				break;
			}
		}

		if (!wantedEntry)
			return false; // No wanted entry

		// Calculate jail time (bounty * 0.0048 seconds, capped)
		float jailTime = Math.Clamp(wantedEntry.m_iTotalBounty * m_fJailFeeMultiplier, m_iMinJailTime, m_iMaxJailTime);

		// Create arrest data
		ref EdenRPArrestData arrestData = new EdenRPArrestData();
		arrestData.m_sPlayerId = playerId;
		arrestData.m_sOfficerId = officerId;
		arrestData.m_iBounty = wantedEntry.m_iTotalBounty;
		arrestData.m_fJailTime = jailTime;
		arrestData.m_fStartTime = GetGame().GetWorld().GetWorldTime();
		arrestData.m_bIsVigilante = isVigilante;

		// Store arrest
		m_mActiveArrests.Set(playerId, arrestData);
		m_aJailedPlayers.Insert(playerId);

		// Remove from wanted list
		m_aWantedList.Remove(wantedIndex);

		// Update vigilante arrests if applicable
		if (isVigilante && wantedEntry.m_iTotalBounty >= 75000)
		{
			AddVigilanteArrest(officerId);
		}

		// Update statistics
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (manager)
		{
			EdenRPPlayerManager playerManager = manager.GetPlayerManager();
			if (playerManager)
			{
				playerManager.UpdatePlayerStatistic(officerId, EdenRPPlayerStats.ARRESTS_MADE, 1);
			}
		}

		Print(string.Format("[EdenRP] Player %1 arrested by %2. Bounty: $%3, Jail Time: %4s", playerId, officerId, arrestData.m_iBounty, jailTime), LogLevel.NORMAL);
		return true;
	}

	//! Process jail timers
	protected void ProcessJailTimers()
	{
		float currentTime = GetGame().GetWorld().GetWorldTime();
		ref array<string> playersToRelease = new array<string>();

		foreach (string playerId, EdenRPArrestData arrestData : m_mActiveArrests)
		{
			float timeServed = currentTime - arrestData.m_fStartTime;
			if (timeServed >= arrestData.m_fJailTime)
			{
				playersToRelease.Insert(playerId);
			}
		}

		// Release players
		foreach (string playerId : playersToRelease)
		{
			ReleasePlayer(playerId);
		}
	}

	//! Release player from jail
	void ReleasePlayer(string playerId)
	{
		if (!m_mActiveArrests.Contains(playerId))
			return;

		// Remove from jail
		m_mActiveArrests.Remove(playerId);

		int index = m_aJailedPlayers.Find(playerId);
		if (index != -1)
		{
			m_aJailedPlayers.Remove(index);
		}

		// TODO: Teleport player out of jail, restore gear, etc.
		// This would require integration with Reforger's player system

		Print(string.Format("[EdenRP] Player %1 released from jail", playerId), LogLevel.NORMAL);
	}

	//! Add vigilante arrest
	void AddVigilanteArrest(string playerId)
	{
		int currentArrests = 0;
		if (m_mVigilanteArrests.Contains(playerId))
		{
			currentArrests = m_mVigilanteArrests.Get(playerId);
		}

		currentArrests++;
		m_mVigilanteArrests.Set(playerId, currentArrests);

		Print(string.Format("[EdenRP] Vigilante %1 now has %2 arrests", playerId, currentArrests), LogLevel.NORMAL);
	}

	//! Issue ticket
	string IssueTicket(string playerId, string officerId, string reason, int amount)
	{
		string ticketId = GenerateTicketId();

		ref EdenRPTicketData ticket = new EdenRPTicketData();
		ticket.m_sTicketId = ticketId;
		ticket.m_sPlayerId = playerId;
		ticket.m_sOfficerId = officerId;
		ticket.m_sReason = reason;
		ticket.m_iAmount = amount;
		ticket.m_fIssuedTime = GetGame().GetWorld().GetWorldTime();
		ticket.m_bIsPaid = false;

		m_aActiveTickets.Insert(ticket);

		Print(string.Format("[EdenRP] Ticket %1 issued to %2 by %3. Amount: $%4", ticketId, playerId, officerId, amount), LogLevel.NORMAL);
		return ticketId;
	}

	//! Pay ticket
	bool PayTicket(string playerId, string ticketId)
	{
		// Find ticket
		ref EdenRPTicketData ticket = null;
		foreach (EdenRPTicketData t : m_aActiveTickets)
		{
			if (t.m_sTicketId == ticketId && t.m_sPlayerId == playerId && !t.m_bIsPaid)
			{
				ticket = t;
				break;
			}
		}

		if (!ticket)
			return false;

		// Process payment
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		if (!playerManager.RemovePlayerCash(playerId, ticket.m_iAmount))
			return false;

		// Mark as paid
		ticket.m_bIsPaid = true;

		// Update statistics
		playerManager.UpdatePlayerStatistic(playerId, EdenRPPlayerStats.TICKETS_PAID, 1);
		playerManager.UpdatePlayerStatistic(playerId, EdenRPPlayerStats.TICKETS_VAL, ticket.m_iAmount);

		Print(string.Format("[EdenRP] Player %1 paid ticket %2 for $%3", playerId, ticketId, ticket.m_iAmount), LogLevel.NORMAL);
		return true;
	}

	//! Generate ticket ID
	protected string GenerateTicketId()
	{
		int timestamp = (int)GetGame().GetWorld().GetWorldTime();
		int random = Math.RandomInt(1000, 9999);
		return string.Format("TKT_%1_%2", timestamp, random);
	}

	//! Process ticket cleanup
	protected void ProcessTicketCleanup()
	{
		float currentTime = GetGame().GetWorld().GetWorldTime();
		float maxTicketAge = 86400; // 24 hours

		for (int i = m_aActiveTickets.Count() - 1; i >= 0; i--)
		{
			ref EdenRPTicketData ticket = m_aActiveTickets[i];
			if (currentTime - ticket.m_fIssuedTime >= maxTicketAge)
			{
				m_aActiveTickets.Remove(i);
			}
		}
	}

	//! Get wanted list
	array<ref EdenRPWantedEntry> GetWantedList()
	{
		return m_aWantedList;
	}

	//! Get player bounty
	int GetPlayerBounty(string playerId)
	{
		foreach (EdenRPWantedEntry entry : m_aWantedList)
		{
			if (entry.m_sPlayerId == playerId)
			{
				return entry.m_iTotalBounty;
			}
		}
		return 0;
	}

	//! Check if player is jailed
	bool IsPlayerJailed(string playerId)
	{
		return m_aJailedPlayers.Find(playerId) != -1;
	}

	//! Get vigilante arrests
	int GetVigilanteArrests(string playerId)
	{
		if (m_mVigilanteArrests.Contains(playerId))
		{
			return m_mVigilanteArrests.Get(playerId);
		}
		return 0;
	}
}