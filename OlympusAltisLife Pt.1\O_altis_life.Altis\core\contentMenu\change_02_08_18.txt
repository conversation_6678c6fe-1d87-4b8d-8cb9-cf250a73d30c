<t align='center' size='2'>Changelog 2-8-18</t><br/>
<t align='center' shadow='1' shadowColor='#ff0000'>Its Thursday My Dudes</t><br/><br/>

<t color='#1BFF00'>Added Features:</t><br/>
• Gang Garage<br/>
--- Accessed only at your gang shed<br/>
--- Any rank can store vehicles and add them to the gang garage<br/>
------ Add vehicles to the gang garage through gang shed menu<br/>
------ Functions similar to claiming an illegal vehicle<br/>
------ Gang vehicles can be stored at any location where personal vehicles can be stored<br/>
--- Rank 2+ can pull vehicles from the gang garage<br/>
------ ALL gang members online at the time will get keys to the vehicle<br/>
------ Any gang member who does not have keys can get them by Windows Key then Get Keys on the vehicle<br/>
--- Max of 20 vehicles per gang<br/>
• Cartel Garages<br/>
--- You can now look at any of the cartel flags and spawn vehicles from your personal garage<br/>
--- Each cap has two spawn points<br/>
• New Market System<br/>
--- 3 Types - Food, Legal, and Illegal<br/>
--- Selling 1 item of one of the above types will cause all others in that type group to go up in price<br/>
--- Prices will not raise/lower over time (by server), market is solely based on what players sell. No random generation of fake numbers, etc.<br/>
• Hotkey to pull spike strips<br/>
--- Bound to Custom Action key 15 (same as medic road kit)<br/>
--- Cops only, civilians still need to use them from the y-menu<br/>
• Sending bones to jail will now give money to cops and vigilantes<br/>
--- Player who tased and/or restrained the individual MUST be either a cop or vigilante<br/>
--- Anyone can send the bones to jail, the individual who tased and/or restrained will get the compensation.<br/>
• Earplugs now have two levels of effectiveness<br/>
• New Earplugs hotkey - Shift + O<br/>
--- Custom Action 12 still works as before<br/>
• Confirmation menus added<br/>
--- When buying a weapon if you already have one<br/>
--- When selling a vehicle<br/>
--- When starting/ending martial law<br/>
• Ability to turn off emergency lights on vehicles<br/>
--- Use windows key option on the vehicle<br/>
• APD Quadbikes for Patrol Officer+<br/>
• Carrier GL Rig at Rebel Clothing Shops for $55,000<br/>
• Medic Hellcat for Coord+<br/>
• Vehicle Lights added<br/>
--- APD Qilin<br/>
--- APD Armed Plane<br/><br/>

<t color='#FFAE00'>Changed:</t><br/>
• Admins will now have their names appear red in the admin menu<br/>
• All CSATs and level 5 vests will now be seized by APD's seize button<br/>
• Link to reach Support Team in Y-menu<br/>
• Jail bomb timer decreased from 20 to 15 minutes<br/>
• Bomb timer(s) will now show for cops that join during an active fed/jail/bw/escort event<br/>
• APD Deputy Tactical Vest re-added<br/>
• APD will now be able to check vehicle speed with all weapons.<br/>
• EOD Vests price lowered to $45,000<br/>
• RPG-7 will now be able to destroy most armored vehicles with 1 hit<br/>
• Compressed several textures to lower mission file size<br/>
• Medic map markers will now use the civilian spawn markers instead of hospitals to generate population number on spawn screen<br/>
• Increased chance of getting a titan from Blackwaters<br/>
• War Market Weapon NPC will now be its own NPC. The clothing shop is still shared on the same npc.<br/>
• All number edit menus will now have a default of 0 instead of 1 - forcing you to enter a qty.<br/>
--- Reminder: You can double click on an item to select it's entire amount!<br/>
• Server 3 lock/unlock script<br/>
--- Server will now be open 24 hours on Saturday and Sunday.<br/>
• Optimized some stuff...<br/><br/>

<t color='#00E4FF'>Fixed:</t><br/>
• Bergen backpacks will now properly hold 120 slots after logging off/restart/disconnect etc.<br/>
• Cartel capture bar will now properly show contested status when capped at 100 percent<br/>
• Cop lethal payouts should now be more reliable<br/>
• Cops restrained next to each other will no longer be restrained indefinitely<br/>
• Gas station robberies will now stop if robber is restrained<br/>
• Killing OR tasing a unit who is in the process of chopping a vehicle will now stop the chopping process<br/>
• Medic Hummingbird and M-900 lights<br/>
• Un-restrain action will now properly cancel if the target is escorted, the unit dies, etc.<br/>
• Medic name not showing up properly on buddy request.<br/>
• Added distance checks to bolt cutters<br/>
• Escort vehicles can no longer be loaded into a Blackfish<br/>
• Cop and Medic lights on the Hellcat should show better.<br/>
• Cop Enter as Passenger will now work on the Qilin<br/>
• 3 to 1 script has been adjusted to properly recognize firearms on nearby players<br/>
• Issues with saving medic gear to the database<br/>
• When dead the timers will now use the server time to patch potential exploit attempts to save gear beyond 15 minutes<br/>
• Dropping keys will now remove the proper person from the vehicle owners list<br/>
--- Also resolves a cop slim jim bug<br/><br/>

<t color='#FF0000'>Removed</t><br/>
• Obsolete licenses from Tanoa will be removed from you upon first connection<br/>
• Shift + 7 handcuff sound<br/>
• Compact NVGs from Blackwater loot<br/>
• Hunting Zone<br/>
• APD Jeeps<br/>