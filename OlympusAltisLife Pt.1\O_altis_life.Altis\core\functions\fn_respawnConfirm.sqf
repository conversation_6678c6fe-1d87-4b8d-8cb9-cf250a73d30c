private '_action';
[] spawn{

  _action = ["Are you sure you want to Re<PERSON>awn?","Confirmation","Yes","No"] call BIS_fnc_guiMessage;

  if (_action) then {
    oev_respawned = true;
    _dam_obj = player;
    _dam_obj setDamage 1;
    [0, format ["%1 is no more.", name player]] remoteExecCall ["OEC_fnc_broadcast", -2];
    [
      ["event","Respawned"],
      ["player",name player],
      ["player_id",getPlayerUID player],
      ["cash_dropped",oev_cash],
      ["position",getPosATL player]
    ] call OEC_fnc_logIt;
  };

};

((findDisplay 49) closeDisplay 2);
