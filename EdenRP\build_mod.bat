@echo off
echo ========================================
echo EdenRP Mod Build Script
echo ========================================
echo.

REM Set paths (adjust these to your Arma Reforger installation)
set WORKBENCH_PATH="C:\Program Files (x86)\Steam\steamapps\common\Arma Reforger\ArmaReforgerTools.exe"
set PROJECT_PATH="%~dp0addon.gproj"
set OUTPUT_PATH="%~dp0build"

echo Checking for Arma Reforger Tools...
if not exist %WORKBENCH_PATH% (
    echo ERROR: Arma Reforger Tools not found at %WORKBENCH_PATH%
    echo Please update the WORKBENCH_PATH variable in this script
    pause
    exit /b 1
)

echo Found Arma Reforger Tools at %WORKBENCH_PATH%
echo.

echo Building EdenRP mod...
echo Project: %PROJECT_PATH%
echo.

REM Create output directory
if not exist %OUTPUT_PATH% mkdir %OUTPUT_PATH%

echo Starting build process...
echo This may take several minutes...
echo.

REM Build the mod using Workbench command line
%WORKBENCH_PATH% -project %PROJECT_PATH% -build -output %OUTPUT_PATH%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo EdenRP mod has been built successfully.
    echo Output location: %OUTPUT_PATH%
    echo.
    echo Next steps:
    echo 1. Test the mod in Arma Reforger
    echo 2. Upload to Steam Workshop using Workbench
    echo 3. Or distribute the build folder directly
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Please check the build log for errors.
    echo Common issues:
    echo - Script compilation errors
    echo - Missing dependencies
    echo - Corrupted project files
    echo.
    echo See PACKAGING_GUIDE.md for troubleshooting help.
    echo.
)

echo Press any key to exit...
pause >nul