//  File: fn_doorManager.sqf
//	Author: <PERSON> "tkc<PERSON><PERSON>" Schultz

//	Description: Locks/Unlocks all doors to a building.
//	1 - Locked
//	0 - Unlocked

params [
	["_building",obj<PERSON>ull,[obj<PERSON><PERSON>]],
	["_mode",-1,[0]]
];
if (_mode isEqualTo -1 || isNull _building) exitWith {};

private _numOfDoors = getNumber(configFile >> "CfgVehicles" >> (typeOf _building) >> "numberOfDoors");
for "_i" from 1 to _numOfDoors do {
	_building setVariable[format["bis_disabled_Door_%1",_i],_mode,true];
};

if (_mode isEqualTo 0) then {
	systemChat "All building doors have been unlocked.";
} else {
	systemChat "All building doors have been locked.";
};