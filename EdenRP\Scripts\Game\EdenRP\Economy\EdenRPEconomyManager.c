//! EdenRP Economy Manager
//! Manages dynamic market system, banking, and economic balance
//! Converted from Olympus Altis Life economy system

//! Market item data structure
class EdenRPMarketItem
{
	string m_sItemName;
	int m_iBasePrice;
	int m_iCurrentPrice;
	int m_iSupply;
	int m_iDemand;
	bool m_bIsLegal;

	void EdenRPMarketItem(string itemName, int basePrice, bool isLegal = true)
	{
		m_sItemName = itemName;
		m_iBasePrice = basePrice;
		m_iCurrentPrice = basePrice;
		m_iSupply = 100;
		m_iDemand = 100;
		m_bIsLegal = isLegal;
	}
}

//! Economy Manager for EdenRP
class EdenRPEconomyManager
{
	// Market system
	protected ref map<string, ref EdenRPMarketItem> m_mMarketItems;
	protected int m_iMarketUpdateInterval = 60000; // 1 minute

	// Banking system
	protected float m_fATMWithdrawFee = 0.02; // 2% fee
	protected int m_iMaxATMWithdraw = 50000;

	void EdenRPEconomyManager()
	{
		m_mMarketItems = new map<string, ref EdenRPMarketItem>();
	}

	//! Initialize the economy manager
	void Initialize()
	{
		Print("[EdenRP] Economy Manager initialized", LogLevel.NORMAL);

		// Initialize market items from Olympus configuration
		InitializeMarketItems();

		// Start market update system
		GetGame().GetCallqueue().CallLater(UpdateMarketPrices, m_iMarketUpdateInterval, true);
	}

	//! Initialize market items based on Olympus configuration
	protected void InitializeMarketItems()
	{
		// Legal items
		AddMarketItem("apple", 65, true);
		AddMarketItem("peach", 68, true);
		AddMarketItem("salema", 45, true);
		AddMarketItem("ornate", 40, true);
		AddMarketItem("mackerel", 175, true);
		AddMarketItem("tuna", 700, true);
		AddMarketItem("saltr", 1450, true);
		AddMarketItem("cement", 1950, true);
		AddMarketItem("glass", 1420, true);
		AddMarketItem("ironr", 1560, true);
		AddMarketItem("copperr", 1750, true);
		AddMarketItem("silverr", 2100, true);
		AddMarketItem("platinumr", 2800, true);
		AddMarketItem("oilp", 3200, true);
		AddMarketItem("diamondc", 3500, true);

		// Illegal items
		AddMarketItem("marijuana", 2500, false);
		AddMarketItem("frogp", 2650, false);
		AddMarketItem("mmushroom", 2750, false);
		AddMarketItem("heroinp", 3500, false);
		AddMarketItem("cocainep", 4000, false);
		AddMarketItem("crystalmeth", 4500, false);
		AddMarketItem("turtle", 3000, false);
		AddMarketItem("moonshine", 1800, false);
		AddMarketItem("moneybag", 5000, false);
		AddMarketItem("goldbar", 95000, false);
	}

	//! Add market item
	void AddMarketItem(string itemName, int basePrice, bool isLegal)
	{
		ref EdenRPMarketItem item = new EdenRPMarketItem(itemName, basePrice, isLegal);
		m_mMarketItems.Set(itemName, item);
	}

	//! Get market item
	EdenRPMarketItem GetMarketItem(string itemName)
	{
		if (m_mMarketItems.Contains(itemName))
			return m_mMarketItems.Get(itemName);

		return null;
	}

	//! Update market prices based on supply and demand
	protected void UpdateMarketPrices()
	{
		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			// Simple supply/demand calculation
			float supplyDemandRatio = (float)item.m_iDemand / (float)item.m_iSupply;
			float priceMultiplier = Math.Clamp(supplyDemandRatio, 0.5, 2.0);

			item.m_iCurrentPrice = (int)(item.m_iBasePrice * priceMultiplier);

			// Gradually restore supply/demand balance
			if (item.m_iSupply < 100)
				item.m_iSupply += 1;
			if (item.m_iDemand > 100)
				item.m_iDemand -= 1;
		}
	}

	//! Process item sale (affects market)
	int SellItem(string itemName, int quantity)
	{
		ref EdenRPMarketItem item = GetMarketItem(itemName);
		if (!item)
			return 0;

		// Increase supply, decrease demand
		item.m_iSupply += quantity;
		item.m_iDemand = Math.Max(50, item.m_iDemand - quantity);

		return item.m_iCurrentPrice * quantity;
	}

	//! Process item purchase (affects market)
	int BuyItem(string itemName, int quantity)
	{
		ref EdenRPMarketItem item = GetMarketItem(itemName);
		if (!item)
			return 0;

		// Decrease supply, increase demand
		item.m_iSupply = Math.Max(50, item.m_iSupply - quantity);
		item.m_iDemand += quantity;

		return item.m_iCurrentPrice * quantity;
	}

	//! Get current market prices
	map<string, int> GetMarketPrices()
	{
		ref map<string, int> prices = new map<string, int>();

		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			prices.Set(itemName, item.m_iCurrentPrice);
		}

		return prices;
	}

	//! Calculate ATM withdrawal fee
	int CalculateATMFee(int amount)
	{
		return (int)(amount * m_fATMWithdrawFee);
	}

	//! Check if ATM withdrawal is valid
	bool IsValidATMWithdrawal(int amount)
	{
		return amount <= m_iMaxATMWithdraw && amount > 0;
	}
}