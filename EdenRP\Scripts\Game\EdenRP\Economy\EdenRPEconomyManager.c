//! EdenRP Economy Manager
//! Manages dynamic market system, banking, and economic balance
//! Converted from Olympus Altis Life economy system

//! Market item configuration structure (from Olympus market system)
class EdenRPMarketConfig
{
	string m_sItemName;
	int m_iMinPrice;
	int m_iMaxPrice;
	int m_iLegality; // -1 = divider, 0 = legal, 1 = illegal
	float m_fDecreaseRate;
	float m_fIncreaseRate;

	void EdenRPMarketConfig(string itemName, int minPrice, int maxPrice, int legality, float decreaseRate, float increaseRate)
	{
		m_sItemName = itemName;
		m_iMinPrice = minPrice;
		m_iMaxPrice = maxPrice;
		m_iLegality = legality;
		m_fDecreaseRate = decreaseRate;
		m_fIncreaseRate = increaseRate;
	}
}

//! Market item data structure
class EdenRPMarketItem
{
	string m_sItemName;
	int m_iBasePrice;
	int m_iCurrentPrice;
	int m_iMinPrice;
	int m_iMaxPrice;
	float m_fPriceAdjustment;
	int m_iSalesCount;
	bool m_bIsLegal;
	float m_fLastUpdateTime;

	void EdenRPMarketItem(string itemName, int basePrice, int minPrice, int maxPrice, bool isLegal = true)
	{
		m_sItemName = itemName;
		m_iBasePrice = basePrice;
		m_iCurrentPrice = basePrice;
		m_iMinPrice = minPrice;
		m_iMaxPrice = maxPrice;
		m_fPriceAdjustment = 0.0;
		m_iSalesCount = 0;
		m_bIsLegal = isLegal;
		m_fLastUpdateTime = 0.0;
	}

	//! Update price based on sales
	void UpdatePrice(float decreaseRate, float increaseRate, int saleAmount)
	{
		if (saleAmount > 0)
		{
			// Selling decreases price
			m_fPriceAdjustment -= (saleAmount * decreaseRate);
		}
		else if (saleAmount < 0)
		{
			// Buying increases price
			m_fPriceAdjustment += (Math.AbsFloat(saleAmount) * increaseRate);
		}

		// Calculate new price
		int newPrice = m_iBasePrice + (int)(m_iBasePrice * m_fPriceAdjustment);
		m_iCurrentPrice = Math.Clamp(newPrice, m_iMinPrice, m_iMaxPrice);

		m_iSalesCount += Math.AbsInt(saleAmount);
		m_fLastUpdateTime = GetGame().GetWorld().GetWorldTime();
	}
}

//! ATM location data
class EdenRPATMLocation
{
	vector m_vPosition;
	bool m_bIsRobbed;
	float m_fRobbedTime;
	int m_iRobbedDuration; // Duration in seconds

	void EdenRPATMLocation(vector position)
	{
		m_vPosition = position;
		m_bIsRobbed = false;
		m_fRobbedTime = 0.0;
		m_iRobbedDuration = 300; // 5 minutes default
	}

	//! Check if ATM is available
	bool IsAvailable()
	{
		if (!m_bIsRobbed)
			return true;

		float currentTime = GetGame().GetWorld().GetWorldTime();
		if (currentTime - m_fRobbedTime >= m_iRobbedDuration)
		{
			m_bIsRobbed = false;
			return true;
		}

		return false;
	}

	//! Rob the ATM
	void RobATM()
	{
		m_bIsRobbed = true;
		m_fRobbedTime = GetGame().GetWorld().GetWorldTime();
	}
}

//! Economy Manager for EdenRP
class EdenRPEconomyManager
{
	// Market system
	protected ref map<string, ref EdenRPMarketItem> m_mMarketItems;
	protected ref map<string, ref EdenRPMarketConfig> m_mMarketConfigs;
	protected ref array<string> m_aMarketVariableNames;
	protected ref array<int> m_aMarketStartPrices;
	protected ref array<int> m_aMarketCurrentPrices;
	protected int m_iMarketUpdateInterval = 60000; // 1 minute
	protected bool m_bMarketResetEnabled = true;

	// Banking system
	protected float m_fATMWithdrawFee = 0.02; // 2% fee
	protected int m_iMaxATMWithdraw = 50000;
	protected int m_iMaxBankTransfer = *********;
	protected float m_fBankTransferTax = 0.05; // 5% tax
	protected int m_iMinTransferForTax = 100000;

	// ATM system
	protected ref map<string, ref EdenRPATMLocation> m_mATMLocations;
	protected bool m_bATMSystemEnabled = true;

	// Server configuration
	protected int m_iServerId = 1;

	void EdenRPEconomyManager()
	{
		m_mMarketItems = new map<string, ref EdenRPMarketItem>();
		m_mMarketConfigs = new map<string, ref EdenRPMarketConfig>();
		m_aMarketVariableNames = new array<string>();
		m_aMarketStartPrices = new array<int>();
		m_aMarketCurrentPrices = new array<int>();
		m_mATMLocations = new map<string, ref EdenRPATMLocation>();
	}

	//! Initialize the economy manager
	void Initialize()
	{
		Print("[EdenRP] Economy Manager initialized", LogLevel.NORMAL);

		// Initialize market items from Olympus configuration
		InitializeMarketItems();

		// Start market update system
		GetGame().GetCallqueue().CallLater(UpdateMarketPrices, m_iMarketUpdateInterval, true);
	}

	//! Initialize market items based on Olympus configuration
	protected void InitializeMarketItems()
	{
		// Market variable names (from Olympus serv_market_varNames)
		m_aMarketVariableNames = {
			"foodDiv", "apple", "peach", "salema", "ornate", "mackerel", "mullet", "catshark", "tuna",
			"legalDiv", "saltr", "cement", "glass", "ironr", "copperr", "silverr", "platinumr", "oilp", "diamondc",
			"illegalDiv", "marijuana", "frogp", "mmushroom", "heroinp", "cocainep", "turtle", "moonshine", "crystalmeth", "moneybag", "goldbar"
		};

		// Initialize market configurations (from Olympus serv_market_config)
		// Format: [ItemName, Lowest, Highest, Flag, Decrease, Increase]

		// Dividers
		AddMarketConfig("foodDiv", 0, 0, -1, 0.0, 0.0);
		AddMarketConfig("legalDiv", 0, 0, -1, 0.0, 0.0);
		AddMarketConfig("illegalDiv", 0, 0, -1, 0.0, 0.0);

		// Food items
		AddMarketConfig("apple", 88, 160, 0, 0.003, 0.001);
		AddMarketConfig("peach", 128, 248, 0, 0.003, 0.001);
		AddMarketConfig("salema", 96, 183, 0, 0.003, 0.001);
		AddMarketConfig("ornate", 96, 183, 0, 0.003, 0.001);
		AddMarketConfig("mackerel", 202, 376, 0, 0.003, 0.001);
		AddMarketConfig("mullet", 224, 416, 0, 0.003, 0.001);
		AddMarketConfig("catshark", 648, 1206, 0, 0.003, 0.001);
		AddMarketConfig("tuna", 1400, 2604, 0, 0.003, 0.001);

		// Legal processed items
		AddMarketConfig("saltr", 2900, 5394, 0, 0.003, 0.001);
		AddMarketConfig("cement", 3900, 7254, 0, 0.003, 0.001);
		AddMarketConfig("glass", 2840, 5284, 0, 0.003, 0.001);
		AddMarketConfig("ironr", 3120, 5808, 0, 0.003, 0.001);
		AddMarketConfig("copperr", 3500, 6510, 0, 0.003, 0.001);
		AddMarketConfig("silverr", 4200, 7812, 0, 0.003, 0.001);
		AddMarketConfig("platinumr", 5600, 10416, 0, 0.003, 0.001);
		AddMarketConfig("oilp", 6400, 11904, 0, 0.003, 0.001);
		AddMarketConfig("diamondc", 7000, 13020, 0, 0.003, 0.001);

		// Illegal items
		AddMarketConfig("marijuana", 3952, 7354, 1, 0.003, 0.001);
		AddMarketConfig("frogp", 5300, 9858, 1, 0.003, 0.001);
		AddMarketConfig("mmushroom", 5500, 10230, 1, 0.003, 0.001);
		AddMarketConfig("heroinp", 7000, 13020, 1, 0.003, 0.001);
		AddMarketConfig("cocainep", 8000, 14880, 1, 0.003, 0.001);
		AddMarketConfig("turtle", 6000, 11160, 1, 0.003, 0.001);
		AddMarketConfig("moonshine", 15960, 29686, 1, 0.003, 0.001);
		AddMarketConfig("crystalmeth", 16812, 31272, 1, 0.003, 0.001);
		AddMarketConfig("moneybag", 17640, 32832, 1, 0.003, 0.001);
		AddMarketConfig("goldbar", 110500, 205530, 1, 0.003, 0.001);

		// Create market items with starting prices
		CreateMarketItem("apple", 124);
		CreateMarketItem("peach", 188);
		CreateMarketItem("salema", 139);
		CreateMarketItem("ornate", 139);
		CreateMarketItem("mackerel", 289);
		CreateMarketItem("mullet", 320);
		CreateMarketItem("catshark", 927);
		CreateMarketItem("tuna", 2002);
		CreateMarketItem("saltr", 4147);
		CreateMarketItem("cement", 5577);
		CreateMarketItem("glass", 4062);
		CreateMarketItem("ironr", 4464);
		CreateMarketItem("copperr", 5005);
		CreateMarketItem("silverr", 6006);
		CreateMarketItem("platinumr", 8008);
		CreateMarketItem("oilp", 9152);
		CreateMarketItem("diamondc", 10010);
		CreateMarketItem("marijuana", 5653);
		CreateMarketItem("frogp", 7579);
		CreateMarketItem("mmushroom", 7865);
		CreateMarketItem("heroinp", 10010);
		CreateMarketItem("cocainep", 11440);
		CreateMarketItem("turtle", 8580);
		CreateMarketItem("moonshine", 22823);
		CreateMarketItem("crystalmeth", 24042);
		CreateMarketItem("moneybag", 25236);
		CreateMarketItem("goldbar", 158015);

		Print(string.Format("[EdenRP] Initialized %1 market items", m_mMarketItems.Count()), LogLevel.NORMAL);
	}

	//! Add market configuration
	protected void AddMarketConfig(string itemName, int minPrice, int maxPrice, int legality, float decreaseRate, float increaseRate)
	{
		ref EdenRPMarketConfig config = new EdenRPMarketConfig(itemName, minPrice, maxPrice, legality, decreaseRate, increaseRate);
		m_mMarketConfigs.Set(itemName, config);
	}

	//! Create market item with starting price
	protected void CreateMarketItem(string itemName, int startingPrice)
	{
		if (!m_mMarketConfigs.Contains(itemName))
			return;

		ref EdenRPMarketConfig config = m_mMarketConfigs.Get(itemName);
		bool isLegal = (config.m_iLegality == 0);

		ref EdenRPMarketItem item = new EdenRPMarketItem(itemName, startingPrice, config.m_iMinPrice, config.m_iMaxPrice, isLegal);
		m_mMarketItems.Set(itemName, item);

		// Add to price arrays
		m_aMarketStartPrices.Insert(startingPrice);
		m_aMarketCurrentPrices.Insert(startingPrice);
	}

	//! Get market item
	EdenRPMarketItem GetMarketItem(string itemName)
	{
		if (m_mMarketItems.Contains(itemName))
			return m_mMarketItems.Get(itemName);

		return null;
	}

	//! Update market prices based on Olympus algorithm
	protected void UpdateMarketPrices()
	{
		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			if (!m_mMarketConfigs.Contains(itemName))
				continue;

			ref EdenRPMarketConfig config = m_mMarketConfigs.Get(itemName);

			// Skip dividers
			if (config.m_iLegality == -1)
				continue;

			// Gradually restore price towards base (market stabilization)
			if (item.m_fPriceAdjustment > 0)
			{
				item.m_fPriceAdjustment -= 0.001; // Slow recovery
			}
			else if (item.m_fPriceAdjustment < 0)
			{
				item.m_fPriceAdjustment += 0.001; // Slow recovery
			}

			// Recalculate current price
			int newPrice = item.m_iBasePrice + (int)(item.m_iBasePrice * item.m_fPriceAdjustment);
			item.m_iCurrentPrice = Math.Clamp(newPrice, item.m_iMinPrice, item.m_iMaxPrice);
		}

		// Update current prices array for synchronization
		UpdateCurrentPricesArray();
	}

	//! Update current prices array for client synchronization
	protected void UpdateCurrentPricesArray()
	{
		m_aMarketCurrentPrices.Clear();

		foreach (string itemName : m_aMarketVariableNames)
		{
			if (m_mMarketItems.Contains(itemName))
			{
				ref EdenRPMarketItem item = m_mMarketItems.Get(itemName);
				m_aMarketCurrentPrices.Insert(item.m_iCurrentPrice);
			}
			else
			{
				m_aMarketCurrentPrices.Insert(0); // Dividers or missing items
			}
		}
	}

	//! Process item sale (affects market using Olympus algorithm)
	int SellItem(string itemName, int quantity)
	{
		ref EdenRPMarketItem item = GetMarketItem(itemName);
		if (!item || !m_mMarketConfigs.Contains(itemName))
			return 0;

		ref EdenRPMarketConfig config = m_mMarketConfigs.Get(itemName);

		// Update price based on sale (selling decreases price)
		item.UpdatePrice(config.m_fDecreaseRate, config.m_fIncreaseRate, quantity);

		return item.m_iCurrentPrice * quantity;
	}

	//! Process item purchase (affects market using Olympus algorithm)
	int BuyItem(string itemName, int quantity)
	{
		ref EdenRPMarketItem item = GetMarketItem(itemName);
		if (!item || !m_mMarketConfigs.Contains(itemName))
			return 0;

		ref EdenRPMarketConfig config = m_mMarketConfigs.Get(itemName);

		// Update price based on purchase (buying increases price)
		item.UpdatePrice(config.m_fDecreaseRate, config.m_fIncreaseRate, -quantity);

		return item.m_iCurrentPrice * quantity;
	}

	//! Get current market prices
	map<string, int> GetMarketPrices()
	{
		ref map<string, int> prices = new map<string, int>();

		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			prices.Set(itemName, item.m_iCurrentPrice);
		}

		return prices;
	}

	//! Calculate ATM withdrawal fee
	int CalculateATMFee(int amount)
	{
		return (int)(amount * m_fATMWithdrawFee);
	}

	//! Check if ATM withdrawal is valid
	bool IsValidATMWithdrawal(int amount)
	{
		return amount <= m_iMaxATMWithdraw && amount > 0;
	}

	//! Process ATM withdrawal
	bool ProcessATMWithdrawal(string playerID, int amount, vector atmPosition)
	{
		// Check if ATM is available
		string atmKey = string.Format("%1_%2_%3", (int)atmPosition[0], (int)atmPosition[1], (int)atmPosition[2]);

		if (m_mATMLocations.Contains(atmKey))
		{
			ref EdenRPATMLocation atm = m_mATMLocations.Get(atmKey);
			if (!atm.IsAvailable())
			{
				return false; // ATM is robbed
			}
		}

		if (!IsValidATMWithdrawal(amount))
			return false;

		// Get player manager to process withdrawal
		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		ref EdenRPPlayerData playerData = playerManager.GetPlayerData(playerID);
		if (!playerData)
			return false;

		int totalCost = amount + CalculateATMFee(amount);

		if (playerData.m_iBankAccount < totalCost)
			return false;

		// Process withdrawal
		playerData.m_iBankAccount -= totalCost;
		playerData.m_iCash += amount;

		// Update statistics
		playerManager.UpdatePlayerStatistic(playerID, EdenRPPlayerStats.MONEY_SPENT, CalculateATMFee(amount));

		return true;
	}

	//! Process bank deposit
	bool ProcessBankDeposit(string playerID, int amount)
	{
		if (amount <= 0)
			return false;

		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		ref EdenRPPlayerData playerData = playerManager.GetPlayerData(playerID);
		if (!playerData)
			return false;

		if (playerData.m_iCash < amount)
			return false;

		// Process deposit
		playerData.m_iCash -= amount;
		playerData.m_iBankAccount += amount;

		return true;
	}

	//! Process bank transfer
	bool ProcessBankTransfer(string fromPlayerID, string toPlayerID, int amount)
	{
		if (amount <= 0 || amount > m_iMaxBankTransfer)
			return false;

		EdenRPManagerComponent manager = EdenRPManagerComponent.GetInstance();
		if (!manager)
			return false;

		EdenRPPlayerManager playerManager = manager.GetPlayerManager();
		if (!playerManager)
			return false;

		ref EdenRPPlayerData fromPlayerData = playerManager.GetPlayerData(fromPlayerID);
		ref EdenRPPlayerData toPlayerData = playerManager.GetPlayerData(toPlayerID);

		if (!fromPlayerData || !toPlayerData)
			return false;

		// Calculate tax
		int tax = 0;
		if (amount >= m_iMinTransferForTax)
		{
			tax = (int)(amount * m_fBankTransferTax);
		}

		int totalCost = amount + tax;

		if (fromPlayerData.m_iBankAccount < totalCost)
			return false;

		// Process transfer
		fromPlayerData.m_iBankAccount -= totalCost;
		toPlayerData.m_iBankAccount += amount;

		// Update statistics
		if (tax > 0)
		{
			playerManager.UpdatePlayerStatistic(fromPlayerID, EdenRPPlayerStats.MONEY_SPENT, tax);
		}

		return true;
	}

	//! Rob ATM
	bool RobATM(vector atmPosition, int robbedDuration = 300)
	{
		string atmKey = string.Format("%1_%2_%3", (int)atmPosition[0], (int)atmPosition[1], (int)atmPosition[2]);

		ref EdenRPATMLocation atm;
		if (m_mATMLocations.Contains(atmKey))
		{
			atm = m_mATMLocations.Get(atmKey);
		}
		else
		{
			atm = new EdenRPATMLocation(atmPosition);
			m_mATMLocations.Set(atmKey, atm);
		}

		if (!atm.IsAvailable())
			return false; // Already robbed

		atm.RobATM();
		atm.m_iRobbedDuration = robbedDuration;

		return true;
	}

	//! Check if ATM is available
	bool IsATMAvailable(vector atmPosition)
	{
		string atmKey = string.Format("%1_%2_%3", (int)atmPosition[0], (int)atmPosition[1], (int)atmPosition[2]);

		if (m_mATMLocations.Contains(atmKey))
		{
			ref EdenRPATMLocation atm = m_mATMLocations.Get(atmKey);
			return atm.IsAvailable();
		}

		return true; // ATM not in system, assume available
	}

	//! Reset market prices (admin function)
	void ResetMarketPrices()
	{
		if (!m_bMarketResetEnabled)
			return;

		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			item.m_iCurrentPrice = item.m_iBasePrice;
			item.m_fPriceAdjustment = 0.0;
			item.m_iSalesCount = 0;
			item.m_fLastUpdateTime = GetGame().GetWorld().GetWorldTime();
		}

		UpdateCurrentPricesArray();
		Print("[EdenRP] Market prices reset to starting values", LogLevel.NORMAL);
	}

	//! Get market statistics
	map<string, int> GetMarketStatistics()
	{
		ref map<string, int> stats = new map<string, int>();

		int totalItems = 0;
		int totalSales = 0;
		int avgPrice = 0;

		foreach (string itemName, EdenRPMarketItem item : m_mMarketItems)
		{
			if (item.m_bIsLegal || !item.m_bIsLegal) // Count all items
			{
				totalItems++;
				totalSales += item.m_iSalesCount;
				avgPrice += item.m_iCurrentPrice;
			}
		}

		if (totalItems > 0)
			avgPrice = avgPrice / totalItems;

		stats.Set("totalItems", totalItems);
		stats.Set("totalSales", totalSales);
		stats.Set("averagePrice", avgPrice);
		stats.Set("serverId", m_iServerId);

		return stats;
	}
}