# EdenRP - Olympus Altis Life to Arma Reforger Conversion Analysis

## Project Overview
EdenRP is a complete conversion of the Olympus Altis Life framework from Arma 3 (SQF) to Arma Reforger (Enforce Script). This document outlines the comprehensive analysis of systems that need to be converted.

## Database Schema Analysis
Based on the MySQL database dump, the following core tables and systems have been identified:

### Player Management
- **players**: Core player data (cash, bank, licenses, stats, etc.)
- **stats**: Detailed player statistics and progression tracking
- **loadouts**: Faction-specific loadout configurations
- **loadoutsNew**: Enhanced loadout system

### Economy Systems
- **market**: Dynamic market pricing system
- **auctions**: Player-to-player auction system
- **depositboxes**: Secure item storage system

### Law Enforcement
- **wanted**: Wanted system for criminals
- **tickets**: Traffic/crime citation system
- **jail**: Incarceration system

### Gang & Territory Systems
- **gangs**: Gang registration and management
- **gangmembers**: Gang membership and ranks
- **gangvehicles**: Gang-owned vehicles
- **gangbldgs**: Gang buildings and bases
- **gangcrates**: Gang storage containers
- **territories**: Territory control system
- **gangwars**: Gang warfare mechanics
- **conquest**: Territory conquest events

### Property & Housing
- **houses**: Player-owned properties
- **crates**: Storage containers linked to houses

### Vehicle Systems
- **vehicles**: Player-owned vehicles with persistence
- **impound**: Vehicle impound system

### Communication & Social
- **messages**: In-game messaging system
- **mail**: Enhanced mail system
- **notes**: Admin notes on players

### Events & Activities
- **fed_results**: Federal Reserve robbery results
- **lottery**: Lottery system
- **hitman**: Hitman contract system

## Core Systems Analysis

### 1. Player System
**SQF Location**: `core/session/`, `core/functions/`
**Key Features**:
- Player data persistence (cash, bank, licenses, stats)
- Multi-faction support (Civilian, Police, Medical, Gang)
- License system with progression
- Statistics tracking (78+ different stats)
- Title/rank system with colors

### 2. Economy System
**SQF Location**: `core/market/`, server `Functions/Market/`
**Key Features**:
- Dynamic market pricing based on supply/demand
- Multi-server market synchronization
- ATM and banking system
- Auction house for player trading
- Deposit box secure storage

### 3. Law Enforcement (APD/AHP)
**SQF Location**: `core/cop/`, server `Functions/WantedSystem/`
**Key Features**:
- Arrest and restraint system
- Wanted level and bounty system
- Ticketing and fine system
- Jail system with time-based release
- Evidence seizure and search
- Rank-based gear access
- Dispatch system
- Vehicle impound system

### 4. Gang & Cartel Systems
**SQF Location**: `core/gangs/`, server `Functions/Gangs/`
**Key Features**:
- Gang creation and management
- Rank hierarchy within gangs
- Gang bank and shared resources
- Territory control and capture
- Gang wars with kill tracking
- Gang buildings and bases
- Gang vehicles with shared access
- Conquest events for territory control

### 5. Civilian Systems
**SQF Location**: `core/civilian/`, `core/actions/`
**Key Features**:
- Legal jobs (mining, fishing, farming, etc.)
- Illegal activities (drug production, smuggling)
- Robbery system (banks, ATMs, shops, players)
- Vigilante system for civilian law enforcement
- License requirements for activities
- Processing and selling mechanics

### 6. Medical/EMS System
**SQF Location**: `core/medical/`
**Key Features**:
- Revive system with bleedout timer
- Medical procedures and equipment
- EMS faction with ranks
- Hospital interactions
- Death penalties and respawn system

### 7. Vehicle & Garage System
**SQF Location**: `core/vehicle/`, `core/shops/`
**Key Features**:
- Vehicle ownership and persistence
- Garage system for storage/retrieval
- Vehicle modification and customization
- Impound system for seized vehicles
- Vehicle insurance and damage system

### 8. Housing & Property System
**SQF Location**: `core/housing/`, server `Functions/Housing/`
**Key Features**:
- Property ownership and persistence
- House storage with capacity limits
- Key sharing system
- Property market and real estate
- Rent and maintenance systems

### 9. Inventory & Item System
**SQF Location**: `core/items/`, `core/functions/`
**Key Features**:
- Virtual and physical inventory
- Item processing and crafting
- Weight and capacity limits
- Item quality and durability
- Special items (licenses, keys, etc.)

### 10. UI/HUD Systems
**SQF Location**: `dialog/`, various UI files
**Key Features**:
- Y-Menu (main interaction menu)
- Shop interfaces (vehicles, items, clothing)
- Admin panels and tools
- Gang management interfaces
- Market and auction interfaces
- Phone/messaging system

## Technical Conversion Challenges

### 1. Architecture Differences
- **SQF → Enforce Script**: Complete language conversion
- **Client-Server Model**: Reforger's authority and replication system
- **Database Integration**: MySQL connectivity in Reforger environment
- **UI Framework**: Reforger's UI system vs Arma 3 dialogs

### 2. Reforger-Specific Considerations
- **Component System**: Entity-component architecture
- **Replication**: Network synchronization
- **Performance**: Optimization for Reforger's engine
- **Modding Framework**: Reforger's mod structure

### 3. Feature Parity Requirements
- **100% Feature Coverage**: All Olympus systems must be converted
- **Data Migration**: Existing player data compatibility
- **Multiplayer Stability**: Server performance under load
- **Anti-Cheat Integration**: Security measures

## Conversion Priority Order

1. **Core Framework Setup** - Basic mod structure and components
2. **Player System** - Authentication, data persistence, factions
3. **Economy System** - Banking, market, basic transactions
4. **Inventory System** - Item management and storage
5. **Vehicle System** - Ownership, garages, basic mechanics
6. **Law Enforcement** - APD systems, arrest, jail
7. **Civilian Activities** - Jobs, legal/illegal activities
8. **Gang Systems** - Gang management, territories, wars
9. **Housing System** - Property ownership and storage
10. **Medical System** - EMS, revive mechanics
11. **UI Development** - All user interfaces
12. **Admin Tools** - Moderation and management
13. **Advanced Features** - Events, conquest, special systems
14. **Anti-Cheat** - Security and validation
15. **Testing & Optimization** - Performance and stability

## Key Configuration Analysis

### Market System Configuration
From `configuration.sqf`, the market system tracks 30+ different items:
- **Legal Items**: Food, processed materials (salt, cement, glass, metals)
- **Illegal Items**: Drugs (marijuana, heroin, cocaine, meth), contraband
- **Special Items**: Gold bars, money bags, turtle (endangered species)

### Faction System
- **APD (Altis Police Department)**: Primary law enforcement
- **AHP (Altis Highway Patrol)**: Specialized traffic enforcement
- **EMS/Medical**: Emergency medical services
- **Civilian**: Default faction with job progression
- **Gang/Cartel**: Criminal organizations with territory control

### Statistics Tracking
78+ different statistics are tracked per player including:
- Resource gathering (mining, fishing, farming)
- Criminal activities (drug production, robberies)
- Law enforcement activities (arrests, tickets)
- Economic activities (money earned/spent)
- Combat statistics (kills, deaths)

## Next Steps
1. Set up basic EdenRP mod structure in Reforger
2. Create core component architecture
3. Implement basic player data system
4. Begin systematic conversion of each major system

This analysis provides the foundation for the complete conversion of Olympus Altis Life to EdenRP for Arma Reforger.